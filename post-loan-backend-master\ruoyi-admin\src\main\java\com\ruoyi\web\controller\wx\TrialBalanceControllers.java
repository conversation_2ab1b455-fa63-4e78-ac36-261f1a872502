package com.ruoyi.web.controller.wx;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.trial_balance.domain.TrialBalance;
import com.ruoyi.trial_balance.service.ITrialBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 试算Controller
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/app/trial_balance")
public class TrialBalanceControllers extends BaseController {
    @Autowired
    private ITrialBalanceService trialBalanceService;

    /**
     * 跟催员查询全额结清和减免结清申请列表
     */
//    @PreAuthorize("@ss.hasPermi('trial_balance:trial_balance:list')")
    @GetMapping("/list")
    public  Map<String, Object> list(TrialBalance trialBalance)
    {
        startPage();
        List<TrialBalance> list = trialBalanceService.selectTrialBalanceList(trialBalance);

        // 拆分 status = 1 的列表
        Optional<TrialBalance> listStatus1 = list.stream()
                .filter(item -> item.getStatus() == 1)
                .findFirst();

        // 拆分 status = 2 的列表
        Optional<TrialBalance> listStatus2 = list.stream()
                .filter(item -> item.getStatus() == 2)
                .findFirst();

        //返回数据给前端

        Map<String, Object> response = new HashMap<>();
        response.put("list1", listStatus1);
        response.put("list2", listStatus2);
        response.put("code", 200);
        return response;
    }



    /**
     * 获取试算详细信息
     */
    @PreAuthorize("@ss.hasPermi('trial_balance:trial_balance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(trialBalanceService.selectTrialBalanceById(id));
    }

    /**
     * 新增试算  发起结清申请
     */
//    @PreAuthorize("@ss.hasPermi('trial_balance:trial_balance:add')")
    @Log(title = "试算", businessType = BusinessType.INSERT)
    @PostMapping()
    @Anonymous
    public AjaxResult add(@RequestBody TrialBalance trialBalance) {
        // 添加逻辑
        return toAjax(trialBalanceService.insertTrialBalance(trialBalance));
    }
}
