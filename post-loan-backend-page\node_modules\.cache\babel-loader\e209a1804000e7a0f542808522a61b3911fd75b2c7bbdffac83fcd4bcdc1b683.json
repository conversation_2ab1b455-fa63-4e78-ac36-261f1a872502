{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _litigation_cost_approval = require(\"@/api/litigation_cost_approval/litigation_cost_approval\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"LitigationCostApproval\",\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 法诉费用审批表格数据\n      litigationCostApprovalList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        approvalStatus: null\n      },\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前案件信息\n      currentCaseInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询法诉费用审批列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _litigation_cost_approval.listLitigationCostApproval)(this.queryParams).then(function (response) {\n        _this.litigationCostApprovalList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation_cost_approval", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "litigationCostApprovalList", "queryParams", "pageNum", "pageSize", "approvalStatus", "detailsDialogVisible", "currentCaseInfo", "feeRecords", "recordsLoading", "selected<PERSON><PERSON><PERSON><PERSON>", "singleApprovalDialogVisible", "singleApprovalForm", "id", "action", "rejectReason", "batchApprovalDialogVisible", "batchApprovalForm", "created", "getList", "methods", "_this", "listLitigationCostApproval", "then", "response", "rows"], "sources": ["src/views/litigation_cost_approval/litigation_cost_approval/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"litigationCostApprovalList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"案件编号\" align=\"center\" prop=\"caseNumber\" />\n      <el-table-column label=\"案件名称\" align=\"center\" prop=\"caseName\" />\n      <el-table-column label=\"被告姓名\" align=\"center\" prop=\"defendantName\" />\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.lawyerFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.litigationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.preservationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.totalMoney || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n            {{ getStatusText(scope.row.approvalStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleViewDetails(scope.row)\"\n            v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\"\n          >审批</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 费用详情审批对话框 -->\n    <el-dialog title=\"法诉费用审批\" :visible.sync=\"detailsDialogVisible\" width=\"1200px\" append-to-body>\n      <!-- 案件基本信息 -->\n      <el-descriptions :column=\"3\" border style=\"margin-bottom: 20px\" v-if=\"currentCaseInfo\">\n        <el-descriptions-item label=\"案件编号\">{{ currentCaseInfo.caseNumber }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件名称\">{{ currentCaseInfo.caseName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告姓名\">{{ currentCaseInfo.defendantName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告身份证\">{{ currentCaseInfo.defendantIdCard }}</el-descriptions-item>\n        <el-descriptions-item label=\"法院管辖\">{{ currentCaseInfo.courtLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件书记员\">{{ currentCaseInfo.curator }}</el-descriptions-item>\n      </el-descriptions>\n\n      <!-- 费用记录列表 -->\n      <div style=\"margin-bottom: 20px;\">\n        <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;\">\n          <h4>费用记录</h4>\n          <div>\n            <el-button\n              type=\"success\"\n              size=\"small\"\n              @click=\"handleBatchApprove('approve')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量通过</el-button>\n            <el-button\n              type=\"danger\"\n              size=\"small\"\n              @click=\"handleBatchApprove('reject')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量拒绝</el-button>\n          </div>\n        </div>\n\n        <el-table\n          :data=\"feeRecords\"\n          @selection-change=\"handleRecordSelectionChange\"\n          v-loading=\"recordsLoading\"\n          border>\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}') }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\n          <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.lawyerFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.litigationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.preservationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.surveillanceFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.announcementFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.appraisalFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.executionFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.totalMoney || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n                {{ getStatusText(scope.row.approvalStatus) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}') : '-' }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\n          <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" show-overflow-tooltip />\n          <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'approve')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >通过</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'reject')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >拒绝</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 单个审批确认对话框 -->\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"singleApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"singleApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 批量审批确认对话框 -->\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"选中记录\">\n          <span>{{ selectedRecords.length }} 条记录</span>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"batchApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"batchApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { \n  listLitigationCostApproval, \n  getLitigationCostSubmissionRecords, \n  singleApproveLitigationCost, \n  batchApproveLitigationCostNew \n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\n\nexport default {\n  name: \"LitigationCostApproval\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 法诉费用审批表格数据\n      litigationCostApprovalList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        approvalStatus: null,\n      },\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前案件信息\n      currentCaseInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询法诉费用审批列表 */\n    getList() {\n      this.loading = true\n      listLitigationCostApproval(this.queryParams).then(response => {\n        this.litigationCostApprovalList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;AAuQA,IAAAA,yBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAOA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,0BAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;MACA;MACA;MACAC,oBAAA;MACA;MACAC,eAAA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,2BAAA;MACAC,kBAAA;QACAC,EAAA;QACAC,MAAA;QAAA;QACAC,YAAA;MACA;MACA;MACAC,0BAAA;MACAC,iBAAA;QACAH,MAAA;QAAA;QACAC,YAAA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,oDAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAApB,0BAAA,GAAAuB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAArB,KAAA,GAAAwB,QAAA,CAAAxB,KAAA;QACAqB,KAAA,CAAA1B,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}