{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es.array.includes.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _litigation_cost_approval = require(\"@/api/litigation_cost_approval/litigation_cost_approval\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"LitigationCostApproval\",\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 法诉费用审批表格数据\n      litigationCostApprovalList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        approvalStatus: null\n      },\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前案件信息\n      currentCaseInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '',\n        // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询法诉费用审批列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _litigation_cost_approval.listLitigationCostApproval)(this.queryParams).then(function (response) {\n        _this.litigationCostApprovalList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('litigation_cost_approval/litigation_cost_approval/export', (0, _objectSpread2.default)({}, this.queryParams), \"litigation_cost_approval_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    /** 获取状态文本 */getStatusText: function getStatusText(status) {\n      var statusMap = {\n        '0': '未审批',\n        '1': '全部同意',\n        '2': '已拒绝',\n        '3': '法诉主管审批',\n        '4': '总监审批',\n        '5': '财务主管/总监抄送',\n        '6': '总经理/董事长审批'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    /** 获取状态标签类型 */getStatusTagType: function getStatusTagType(status) {\n      var typeMap = {\n        '0': 'info',\n        '1': 'success',\n        '2': 'danger',\n        '3': 'warning',\n        '4': 'warning',\n        '5': 'warning',\n        '6': 'warning'\n      };\n      return typeMap[status] || 'info';\n    },\n    /** 检查是否可以审批 */canApprove: function canApprove(status) {\n      return ['3', '4', '5', '6'].includes(status);\n    },\n    /** 查看费用详情和审批 */handleViewDetails: function handleViewDetails(row) {\n      this.currentCaseInfo = row;\n      this.detailsDialogVisible = true;\n      this.loadFeeRecords(row.litigationCaseId);\n    },\n    /** 加载费用记录 */loadFeeRecords: function loadFeeRecords(litigationCaseId) {\n      var _this2 = this;\n      this.recordsLoading = true;\n      (0, _litigation_cost_approval.getLitigationCostSubmissionRecords)(litigationCaseId).then(function (response) {\n        _this2.feeRecords = response.data || [];\n        _this2.recordsLoading = false;\n      }).catch(function () {\n        _this2.recordsLoading = false;\n        _this2.$modal.msgError('加载费用记录失败');\n      });\n    },\n    /** 费用记录选择变化 */handleRecordSelectionChange: function handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection;\n    },\n    /** 单个审批 */handleSingleApprove: function handleSingleApprove(record, action) {\n      this.singleApprovalForm.id = record.id;\n      this.singleApprovalForm.action = action;\n      this.singleApprovalForm.rejectReason = '';\n      this.singleApprovalDialogVisible = true;\n    },\n    /** 确认单个审批 */confirmSingleApproval: function confirmSingleApproval() {\n      var _this3 = this;\n      if (this.singleApprovalForm.action === 'reject') {\n        this.$refs[\"singleApprovalForm\"].validate(function (valid) {\n          if (!valid) return;\n          _this3.executeSingleApproval();\n        });\n      } else {\n        this.executeSingleApproval();\n      }\n    },\n    /** 执行单个审批 */executeSingleApproval: function executeSingleApproval() {\n      var _this4 = this;\n      var data = {\n        id: this.singleApprovalForm.id,\n        action: this.singleApprovalForm.action,\n        rejectReason: this.singleApprovalForm.rejectReason\n      };\n      (0, _litigation_cost_approval.singleApproveLitigationCost)(data).then(function () {\n        _this4.$modal.msgSuccess(\"\".concat(_this4.singleApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n        _this4.singleApprovalDialogVisible = false;\n        _this4.loadFeeRecords(_this4.currentCaseInfo.litigationCaseId);\n        _this4.getList();\n      }).catch(function () {\n        _this4.$modal.msgError('审批失败');\n      });\n    },\n    /** 批量审批 */handleBatchApprove: function handleBatchApprove(action) {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError('请选择要审批的记录');\n        return;\n      }\n      this.batchApprovalForm.action = action;\n      this.batchApprovalForm.rejectReason = '';\n      this.batchApprovalDialogVisible = true;\n    },\n    /** 确认批量审批 */confirmBatchApproval: function confirmBatchApproval() {\n      var _this5 = this;\n      if (this.batchApprovalForm.action === 'reject') {\n        this.$refs[\"batchApprovalForm\"].validate(function (valid) {\n          if (!valid) return;\n          _this5.executeBatchApproval();\n        });\n      } else {\n        this.executeBatchApproval();\n      }\n    },\n    /** 执行批量审批 */executeBatchApproval: function executeBatchApproval() {\n      var _this6 = this;\n      var data = {\n        ids: this.selectedRecords.map(function (record) {\n          return record.id;\n        }),\n        action: this.batchApprovalForm.action,\n        rejectReason: this.batchApprovalForm.rejectReason\n      };\n      (0, _litigation_cost_approval.batchApproveLitigationCostNew)(data).then(function () {\n        _this6.$modal.msgSuccess(\"\\u6279\\u91CF\".concat(_this6.batchApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n        _this6.batchApprovalDialogVisible = false;\n        _this6.loadFeeRecords(_this6.currentCaseInfo.litigationCaseId);\n        _this6.getList();\n      }).catch(function () {\n        _this6.$modal.msgError('批量审批失败');\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation_cost_approval", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "litigationCostApprovalList", "queryParams", "pageNum", "pageSize", "approvalStatus", "detailsDialogVisible", "currentCaseInfo", "feeRecords", "recordsLoading", "selected<PERSON><PERSON><PERSON><PERSON>", "singleApprovalDialogVisible", "singleApprovalForm", "id", "action", "rejectReason", "batchApprovalDialogVisible", "batchApprovalForm", "created", "getList", "methods", "_this", "listLitigationCostApproval", "then", "response", "rows", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "map", "item", "length", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "getStatusText", "status", "statusMap", "getStatusTagType", "typeMap", "canApprove", "includes", "handleViewDetails", "row", "loadFeeRecords", "litigationCaseId", "_this2", "getLitigationCostSubmissionRecords", "catch", "$modal", "msgError", "handleRecordSelectionChange", "handleSingleApprove", "record", "confirmSingleApproval", "_this3", "$refs", "validate", "valid", "executeSingleApproval", "_this4", "singleApproveLitigationCost", "msgSuccess", "handleBatchApprove", "confirmBatchApproval", "_this5", "executeBatchApproval", "_this6", "batchApproveLitigationCostNew"], "sources": ["src/views/litigation_cost_approval/litigation_cost_approval/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"litigationCostApprovalList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"案件编号\" align=\"center\" prop=\"caseNumber\" />\n      <el-table-column label=\"案件名称\" align=\"center\" prop=\"caseName\" />\n      <el-table-column label=\"被告姓名\" align=\"center\" prop=\"defendantName\" />\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.lawyerFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.litigationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.preservationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.totalMoney || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n            {{ getStatusText(scope.row.approvalStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleViewDetails(scope.row)\"\n            v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\"\n          >审批</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 费用详情审批对话框 -->\n    <el-dialog title=\"法诉费用审批\" :visible.sync=\"detailsDialogVisible\" width=\"1200px\" append-to-body>\n      <!-- 案件基本信息 -->\n      <el-descriptions :column=\"3\" border style=\"margin-bottom: 20px\" v-if=\"currentCaseInfo\">\n        <el-descriptions-item label=\"案件编号\">{{ currentCaseInfo.caseNumber }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件名称\">{{ currentCaseInfo.caseName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告姓名\">{{ currentCaseInfo.defendantName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告身份证\">{{ currentCaseInfo.defendantIdCard }}</el-descriptions-item>\n        <el-descriptions-item label=\"法院管辖\">{{ currentCaseInfo.courtLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件书记员\">{{ currentCaseInfo.curator }}</el-descriptions-item>\n      </el-descriptions>\n\n      <!-- 费用记录列表 -->\n      <div style=\"margin-bottom: 20px;\">\n        <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;\">\n          <h4>费用记录</h4>\n          <div>\n            <el-button\n              type=\"success\"\n              size=\"small\"\n              @click=\"handleBatchApprove('approve')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量通过</el-button>\n            <el-button\n              type=\"danger\"\n              size=\"small\"\n              @click=\"handleBatchApprove('reject')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量拒绝</el-button>\n          </div>\n        </div>\n\n        <el-table\n          :data=\"feeRecords\"\n          @selection-change=\"handleRecordSelectionChange\"\n          v-loading=\"recordsLoading\"\n          border>\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}') }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\n          <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.lawyerFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.litigationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.preservationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.surveillanceFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.announcementFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.appraisalFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.executionFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.totalMoney || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n                {{ getStatusText(scope.row.approvalStatus) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}') : '-' }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\n          <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" show-overflow-tooltip />\n          <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'approve')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >通过</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'reject')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >拒绝</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 单个审批确认对话框 -->\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"singleApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"singleApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 批量审批确认对话框 -->\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"选中记录\">\n          <span>{{ selectedRecords.length }} 条记录</span>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"batchApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"batchApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { \n  listLitigationCostApproval, \n  getLitigationCostSubmissionRecords, \n  singleApproveLitigationCost, \n  batchApproveLitigationCostNew \n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\n\nexport default {\n  name: \"LitigationCostApproval\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 法诉费用审批表格数据\n      litigationCostApprovalList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        approvalStatus: null,\n      },\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前案件信息\n      currentCaseInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询法诉费用审批列表 */\n    getList() {\n      this.loading = true\n      listLitigationCostApproval(this.queryParams).then(response => {\n        this.litigationCostApprovalList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\n        ...this.queryParams\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\n    },\n\n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        '0': '未审批',\n        '1': '全部同意',\n        '2': '已拒绝',\n        '3': '法诉主管审批',\n        '4': '总监审批',\n        '5': '财务主管/总监抄送',\n        '6': '总经理/董事长审批'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const typeMap = {\n        '0': 'info',\n        '1': 'success',\n        '2': 'danger',\n        '3': 'warning',\n        '4': 'warning',\n        '5': 'warning',\n        '6': 'warning'\n      }\n      return typeMap[status] || 'info'\n    },\n    /** 检查是否可以审批 */\n    canApprove(status) {\n      return ['3', '4', '5', '6'].includes(status)\n    },\n\n    /** 查看费用详情和审批 */\n    handleViewDetails(row) {\n      this.currentCaseInfo = row\n      this.detailsDialogVisible = true\n      this.loadFeeRecords(row.litigationCaseId)\n    },\n\n    /** 加载费用记录 */\n    loadFeeRecords(litigationCaseId) {\n      this.recordsLoading = true\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\n        this.feeRecords = response.data || []\n        this.recordsLoading = false\n      }).catch(() => {\n        this.recordsLoading = false\n        this.$modal.msgError('加载费用记录失败')\n      })\n    },\n\n    /** 费用记录选择变化 */\n    handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection\n    },\n\n    /** 单个审批 */\n    handleSingleApprove(record, action) {\n      this.singleApprovalForm.id = record.id\n      this.singleApprovalForm.action = action\n      this.singleApprovalForm.rejectReason = ''\n      this.singleApprovalDialogVisible = true\n    },\n\n    /** 确认单个审批 */\n    confirmSingleApproval() {\n      if (this.singleApprovalForm.action === 'reject') {\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\n          if (!valid) return\n          this.executeSingleApproval()\n        })\n      } else {\n        this.executeSingleApproval()\n      }\n    },\n\n    /** 执行单个审批 */\n    executeSingleApproval() {\n      const data = {\n        id: this.singleApprovalForm.id,\n        action: this.singleApprovalForm.action,\n        rejectReason: this.singleApprovalForm.rejectReason\n      }\n\n      singleApproveLitigationCost(data).then(() => {\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\n        this.singleApprovalDialogVisible = false\n        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)\n        this.getList()\n      }).catch(() => {\n        this.$modal.msgError('审批失败')\n      })\n    },\n\n    /** 批量审批 */\n    handleBatchApprove(action) {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError('请选择要审批的记录')\n        return\n      }\n\n      this.batchApprovalForm.action = action\n      this.batchApprovalForm.rejectReason = ''\n      this.batchApprovalDialogVisible = true\n    },\n\n    /** 确认批量审批 */\n    confirmBatchApproval() {\n      if (this.batchApprovalForm.action === 'reject') {\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\n          if (!valid) return\n          this.executeBatchApproval()\n        })\n      } else {\n        this.executeBatchApproval()\n      }\n    },\n\n    /** 执行批量审批 */\n    executeBatchApproval() {\n      const data = {\n        ids: this.selectedRecords.map(record => record.id),\n        action: this.batchApprovalForm.action,\n        rejectReason: this.batchApprovalForm.rejectReason\n      }\n\n      batchApproveLitigationCostNew(data).then(() => {\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\n        this.batchApprovalDialogVisible = false\n        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)\n        this.getList()\n      }).catch(() => {\n        this.$modal.msgError('批量审批失败')\n      })\n    }\n  }\n}\n</script>\n"], "mappings": ";;;;;;;;;;;;;AAuQA,IAAAA,yBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAOA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,0BAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;MACA;MACA;MACAC,oBAAA;MACA;MACAC,eAAA;MACA;MACAC,UAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,2BAAA;MACAC,kBAAA;QACAC,EAAA;QACAC,MAAA;QAAA;QACAC,YAAA;MACA;MACA;MACAC,0BAAA;MACAC,iBAAA;QACAH,MAAA;QAAA;QACAC,YAAA;MACA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,oDAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAApB,0BAAA,GAAAuB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAArB,KAAA,GAAAwB,QAAA,CAAAxB,KAAA;QACAqB,KAAA,CAAA1B,OAAA;MACA;IACA;IACA,aACA+B,WAAA,WAAAA,YAAA;MACA,KAAAxB,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IACA,aACAQ,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlC,GAAA,GAAAkC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAnB,EAAA;MAAA;MACA,KAAAhB,MAAA,GAAAiC,SAAA,CAAAG,MAAA;MACA,KAAAnC,QAAA,IAAAgC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iEAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnC,WAAA,+BAAAoC,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEA,aACAC,aAAA,WAAAA,cAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA,eACAE,gBAAA,WAAAA,iBAAAF,MAAA;MACA,IAAAG,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAH,MAAA;IACA;IACA,eACAI,UAAA,WAAAA,WAAAJ,MAAA;MACA,4BAAAK,QAAA,CAAAL,MAAA;IACA;IAEA,gBACAM,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAA1C,eAAA,GAAA0C,GAAA;MACA,KAAA3C,oBAAA;MACA,KAAA4C,cAAA,CAAAD,GAAA,CAAAE,gBAAA;IACA;IAEA,aACAD,cAAA,WAAAA,eAAAC,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA3C,cAAA;MACA,IAAA4C,4DAAA,EAAAF,gBAAA,EAAA5B,IAAA,WAAAC,QAAA;QACA4B,MAAA,CAAA5C,UAAA,GAAAgB,QAAA,CAAA9B,IAAA;QACA0D,MAAA,CAAA3C,cAAA;MACA,GAAA6C,KAAA;QACAF,MAAA,CAAA3C,cAAA;QACA2C,MAAA,CAAAG,MAAA,CAAAC,QAAA;MACA;IACA;IAEA,eACAC,2BAAA,WAAAA,4BAAA3B,SAAA;MACA,KAAApB,eAAA,GAAAoB,SAAA;IACA;IAEA,WACA4B,mBAAA,WAAAA,oBAAAC,MAAA,EAAA7C,MAAA;MACA,KAAAF,kBAAA,CAAAC,EAAA,GAAA8C,MAAA,CAAA9C,EAAA;MACA,KAAAD,kBAAA,CAAAE,MAAA,GAAAA,MAAA;MACA,KAAAF,kBAAA,CAAAG,YAAA;MACA,KAAAJ,2BAAA;IACA;IAEA,aACAiD,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,SAAAjD,kBAAA,CAAAE,MAAA;QACA,KAAAgD,KAAA,uBAAAC,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;UACAH,MAAA,CAAAI,qBAAA;QACA;MACA;QACA,KAAAA,qBAAA;MACA;IACA;IAEA,aACAA,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,IAAAxE,IAAA;QACAmB,EAAA,OAAAD,kBAAA,CAAAC,EAAA;QACAC,MAAA,OAAAF,kBAAA,CAAAE,MAAA;QACAC,YAAA,OAAAH,kBAAA,CAAAG;MACA;MAEA,IAAAoD,qDAAA,EAAAzE,IAAA,EAAA6B,IAAA;QACA2C,MAAA,CAAAX,MAAA,CAAAa,UAAA,IAAA9B,MAAA,CAAA4B,MAAA,CAAAtD,kBAAA,CAAAE,MAAA;QACAoD,MAAA,CAAAvD,2BAAA;QACAuD,MAAA,CAAAhB,cAAA,CAAAgB,MAAA,CAAA3D,eAAA,CAAA4C,gBAAA;QACAe,MAAA,CAAA/C,OAAA;MACA,GAAAmC,KAAA;QACAY,MAAA,CAAAX,MAAA,CAAAC,QAAA;MACA;IACA;IAEA,WACAa,kBAAA,WAAAA,mBAAAvD,MAAA;MACA,SAAAJ,eAAA,CAAAuB,MAAA;QACA,KAAAsB,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAAvC,iBAAA,CAAAH,MAAA,GAAAA,MAAA;MACA,KAAAG,iBAAA,CAAAF,YAAA;MACA,KAAAC,0BAAA;IACA;IAEA,aACAsD,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAtD,iBAAA,CAAAH,MAAA;QACA,KAAAgD,KAAA,sBAAAC,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;UACAO,MAAA,CAAAC,oBAAA;QACA;MACA;QACA,KAAAA,oBAAA;MACA;IACA;IAEA,aACAA,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAA/E,IAAA;QACAE,GAAA,OAAAc,eAAA,CAAAqB,GAAA,WAAA4B,MAAA;UAAA,OAAAA,MAAA,CAAA9C,EAAA;QAAA;QACAC,MAAA,OAAAG,iBAAA,CAAAH,MAAA;QACAC,YAAA,OAAAE,iBAAA,CAAAF;MACA;MAEA,IAAA2D,uDAAA,EAAAhF,IAAA,EAAA6B,IAAA;QACAkD,MAAA,CAAAlB,MAAA,CAAAa,UAAA,gBAAA9B,MAAA,CAAAmC,MAAA,CAAAxD,iBAAA,CAAAH,MAAA;QACA2D,MAAA,CAAAzD,0BAAA;QACAyD,MAAA,CAAAvB,cAAA,CAAAuB,MAAA,CAAAlE,eAAA,CAAA4C,gBAAA;QACAsB,MAAA,CAAAtD,OAAA;MACA,GAAAmC,KAAA;QACAmB,MAAA,CAAAlB,MAAA,CAAAC,QAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}