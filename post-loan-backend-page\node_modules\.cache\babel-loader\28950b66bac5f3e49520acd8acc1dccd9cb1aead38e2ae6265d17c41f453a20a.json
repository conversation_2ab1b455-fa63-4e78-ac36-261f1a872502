{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.array.concat.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _litigation_cost_approval = require(\"@/api/litigation_cost_approval/litigation_cost_approval\");\nvar _area = _interopRequireDefault(require(\"../../../assets/area.json\"));\nvar _userInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/userInfo.vue\"));\nvar _approvalStatus = _interopRequireDefault(require(\"@/utils/approvalStatus\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"Vm_car_order\",\n  components: {\n    userInfo: _userInfo.default\n  },\n  data: function data() {\n    return (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // VIEW表格数据\n      vm_car_orderList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        // 严格按照9个筛选条件\n        // 1. 贷款人姓名\n        customerName: null,\n        // 2. 贷款人身份证号\n        certId: null,\n        // 3. 出单渠道\n        jgName: null,\n        // 4. 放款银行\n        lendingBank: null,\n        // 5. 法诉状态(多级)\n        litigationStatus: null,\n        // 6. 申请人\n        applicationBy: null,\n        // 7. 费用类型\n        costCategory: null,\n        // 8. 审批状态\n        approvalStatus: null,\n        // 9. 申请时间区间\n        startTime: null,\n        endTime: null,\n        // 10. 审批时间区间\n        approvalStartTime: null,\n        approvalEndTime: null\n      },\n      // 日期范围\n      dateRange: [],\n      // 审批日期范围\n      approvalDateRange: [],\n      // 表单参数\n      form: {\n        id: '',\n        status: 0,\n        rejectReason: null\n      },\n      // 当前审批记录\n      currentRecord: {},\n      // 费用提交记录列表\n      submissionRecords: [],\n      // 记录加载状态\n      recordsLoading: false,\n      // 选中的记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalOpen: false,\n      singleApprovalForm: {\n        id: '',\n        status: '0',\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalOpen: false,\n      batchApprovalForm: {\n        status: '0',\n        rejectReason: ''\n      },\n      // 单个审批对话框\n      singleApprovalDialogVisible: false\n    }, \"singleApprovalForm\", {\n      id: '',\n      action: '',\n      // 'approve' 或 'reject'\n      rejectReason: ''\n    }), \"batchApprovalDialogVisible\", false), \"batchApprovalForm\", {\n      action: '',\n      // 'approve' 或 'reject'\n      rejectReason: ''\n    }), \"userInfoVisible\", false), \"customerInfo\", {}), \"litigationStatusOptions\", [{\n      value: '起诉',\n      label: '起诉',\n      children: [{\n        value: '起诉-准备材料',\n        label: '准备材料'\n      }, {\n        value: '起诉-已提交',\n        label: '已提交'\n      }, {\n        value: '起诉-法院受理',\n        label: '法院受理'\n      }]\n    }, {\n      value: '审理',\n      label: '审理',\n      children: [{\n        value: '审理-开庭审理',\n        label: '开庭审理'\n      }, {\n        value: '审理-等待判决',\n        label: '等待判决'\n      }, {\n        value: '审理-一审判决',\n        label: '一审判决'\n      }]\n    }, {\n      value: '执行',\n      label: '执行',\n      children: [{\n        value: '执行-申请执行',\n        label: '申请执行'\n      }, {\n        value: '执行-执行中',\n        label: '执行中'\n      }, {\n        value: '执行-执行完毕',\n        label: '执行完毕'\n      }]\n    }, {\n      value: '结案',\n      label: '结案',\n      children: [{\n        value: '结案-胜诉结案',\n        label: '胜诉结案'\n      }, {\n        value: '结案-败诉结案',\n        label: '败诉结案'\n      }, {\n        value: '结案-和解结案',\n        label: '和解结案'\n      }]\n    }]), \"rules\", {\n      keyProvince: '',\n      keyCity: '',\n      keyBorough: '',\n      keyDetailAddress: ''\n    }), \"provinceList\", _area.default), \"cityList\", []), \"districtList\", []);\n  },\n  created: function created() {\n    this.getList();\n  },\n  methods: (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({\n    /** 查询法诉费用审批列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _litigation_cost_approval.listLitigationCostApproval)(this.queryParams).then(function (response) {\n        _this.vm_car_orderList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel: function cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        id: '',\n        status: 0,\n        rejectReason: null\n      };\n      this.currentRecord = {};\n      this.submissionRecords = [];\n      this.selectedRecords = [];\n      this.singleApprovalOpen = false;\n      this.batchApprovalOpen = false;\n      this.singleApprovalForm = {\n        id: '',\n        status: '0',\n        rejectReason: ''\n      };\n      this.batchApprovalForm = {\n        status: '0',\n        rejectReason: ''\n      };\n      this.singleApprovalDialogVisible = false;\n      this.singleApprovalForm = {\n        id: '',\n        action: '',\n        rejectReason: ''\n      };\n      this.batchApprovalDialogVisible = false;\n      this.batchApprovalForm = {\n        action: '',\n        rejectReason: ''\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.dateRange = [];\n      this.approvalDateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加VIEW\";\n    },\n    /** 修改按钮操作 */handleUpdate: function handleUpdate(row) {\n      this.currentRecord = row;\n      this.loadSubmissionRecords(row.litigationCaseId);\n      this.open = true;\n    },\n    /** 批量修改按钮操作 */handleBatchEdit: function handleBatchEdit() {\n      this.$modal.msgError('请选择单条记录进行审批操作');\n    },\n    /** 加载费用提交记录 */loadSubmissionRecords: function loadSubmissionRecords(litigationCaseId) {\n      var _this2 = this;\n      this.recordsLoading = true;\n      (0, _litigation_cost_approval.getLitigationCostSubmissionRecords)(litigationCaseId).then(function (response) {\n        _this2.submissionRecords = response.data || [];\n        _this2.recordsLoading = false;\n      }).catch(function () {\n        _this2.recordsLoading = false;\n      });\n    },\n    /** 记录选择变化 */handleRecordSelectionChange: function handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection;\n    },\n    /** 单个审批 */handleSingleApprove: function handleSingleApprove(record, status) {\n      this.singleApprovalForm.id = record.id;\n      this.singleApprovalForm.status = status;\n      this.singleApprovalForm.rejectReason = '';\n      this.singleApprovalOpen = true;\n    },\n    /** 确认单个审批 */confirmSingleApproval: function confirmSingleApproval() {\n      var _this3 = this;\n      if (this.singleApprovalForm.status == '1' && !this.singleApprovalForm.rejectReason) {\n        this.$modal.msgError('请输入拒绝原因');\n        return;\n      }\n      (0, _litigation_cost_approval.approveLitigationCostRecord)(this.singleApprovalForm).then(function () {\n        _this3.$modal.msgSuccess('审批成功');\n        _this3.singleApprovalOpen = false;\n        _this3.loadSubmissionRecords(_this3.currentRecord.litigationCaseId);\n        _this3.getList(); // 刷新主列表\n      });\n    },\n    /** 批量审批 */handleBatchApprove: function handleBatchApprove(status) {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError('请选择要审批的记录');\n        return;\n      }\n      this.batchApprovalForm.status = status;\n      this.batchApprovalForm.rejectReason = '';\n      this.batchApprovalOpen = true;\n    },\n    /** 确认批量审批 */confirmBatchApproval: function confirmBatchApproval() {\n      var _this4 = this;\n      if (this.batchApprovalForm.status == '1' && !this.batchApprovalForm.rejectReason) {\n        this.$modal.msgError('请输入拒绝原因');\n        return;\n      }\n      var data = {\n        ids: this.selectedRecords.map(function (record) {\n          return record.id;\n        }),\n        status: this.batchApprovalForm.status,\n        rejectReason: this.batchApprovalForm.rejectReason\n      };\n      (0, _litigation_cost_approval.batchApproveLitigationCostRecords)(data).then(function () {\n        _this4.$modal.msgSuccess('批量审批成功');\n        _this4.batchApprovalOpen = false;\n        _this4.selectedRecords = [];\n        _this4.loadSubmissionRecords(_this4.currentRecord.litigationCaseId);\n        _this4.getList(); // 刷新主列表\n      });\n    },\n    /** 主列表批量审批 */handleBatchApproveMain: function handleBatchApproveMain(status) {\n      var _this5 = this;\n      if (this.ids.length === 0) {\n        this.$modal.msgError('请选择要审批的记录');\n        return;\n      }\n      var statusText = status === '0' ? '通过' : '拒绝';\n      this.$modal.confirm(\"\\u786E\\u8BA4\\u8981\\u6279\\u91CF\".concat(statusText, \"\\u9009\\u4E2D\\u7684 \").concat(this.ids.length, \" \\u6761\\u8BB0\\u5F55\\u5417\\uFF1F\")).then(function () {\n        var data = {\n          ids: _this5.ids,\n          status: status,\n          rejectReason: status === '1' ? '批量拒绝' : ''\n        };\n        return (0, _litigation_cost_approval.batchApproveLitigationCostRecords)(data);\n      }).then(function () {\n        _this5.$modal.msgSuccess(\"\\u6279\\u91CF\".concat(statusText, \"\\u6210\\u529F\"));\n        _this5.getList();\n      }).catch(function () {});\n    },\n    /** 删除按钮操作 */handleDelete: function handleDelete() {\n      var _this6 = this;\n      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {\n        // 这里可以调用删除API，暂时只是提示\n        _this6.$modal.msgSuccess(\"删除功能暂未实现\");\n      }).catch(function () {});\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('litigation_cost_approval/litigation_cost_approval/export', (0, _objectSpread2.default)({}, this.queryParams), \"litigation_cost_approval_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    /** 打开贷款人信息 */openUserInfo: function openUserInfo(row) {\n      if (!row.customerId && !row.applyId) {\n        this.$modal.msgError('无法获取贷款人信息');\n        return;\n      }\n      this.customerInfo = {\n        customerId: row.customerId,\n        applyId: row.applyId,\n        customerName: row.customerName\n      };\n      this.userInfoVisible = true;\n    },\n    /** 处理日期范围变化 */handleDateRangeChange: function handleDateRangeChange(dates) {\n      if (dates && dates.length === 2) {\n        this.queryParams.startTime = dates[0];\n        this.queryParams.endTime = dates[1];\n      } else {\n        this.queryParams.startTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.handleQuery();\n    },\n    /** 处理审批时间范围变化 */handleApprovalDateRangeChange: function handleApprovalDateRangeChange(dates) {\n      if (dates && dates.length === 2) {\n        this.queryParams.approvalStartTime = dates[0];\n        this.queryParams.approvalEndTime = dates[1];\n      } else {\n        this.queryParams.approvalStartTime = null;\n        this.queryParams.approvalEndTime = null;\n      }\n      this.handleQuery();\n    },\n    /** 获取状态文本 */getStatusText: function getStatusText(status) {\n      var statusMap = {\n        0: '未审批',\n        1: '全部通过',\n        3: '法诉主管审批',\n        4: '总监审批',\n        5: '总监抄送',\n        6: '总经理/董事长审批(抄送)',\n        7: '已拒绝'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    /** 获取状态标签类型 */getStatusTagType: function getStatusTagType(status) {\n      switch (status) {\n        case 0:\n          return 'info';\n        case 1:\n          return 'success';\n        case 7:\n          return 'danger';\n        case 3:\n        case 4:\n        case 5:\n        case 6:\n          return 'warning';\n        default:\n          return 'info';\n      }\n    },\n    /** 检查记录是否可以审批 */canApproveRecord: function canApproveRecord(record) {\n      // 只有未审批的记录可以审批\n      return record.approvalStatus === '0' || record.approvalStatus === null || record.approvalStatus === '';\n    }\n  }, \"handleSingleApprove\", function handleSingleApprove(record, action) {\n    this.singleApprovalForm.id = record.id;\n    this.singleApprovalForm.action = action;\n    this.singleApprovalForm.rejectReason = '';\n    this.singleApprovalDialogVisible = true;\n  }), \"confirmSingleApproval\", function confirmSingleApproval() {\n    var _this7 = this;\n    if (this.singleApprovalForm.action === 'reject') {\n      this.$refs[\"singleApprovalForm\"].validate(function (valid) {\n        if (!valid) return;\n        _this7.executeSingleApproval();\n      });\n    } else {\n      this.executeSingleApproval();\n    }\n  }), \"executeSingleApproval\", function executeSingleApproval() {\n    var _this8 = this;\n    var data = {\n      id: this.singleApprovalForm.id,\n      status: this.singleApprovalForm.action === 'approve' ? '1' : '7',\n      rejectReason: this.singleApprovalForm.rejectReason\n    };\n    (0, _litigation_cost_approval.approveLitigationCostRecord)(data).then(function () {\n      _this8.$modal.msgSuccess(\"\".concat(_this8.singleApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n      _this8.singleApprovalDialogVisible = false;\n      _this8.loadSubmissionRecords(_this8.currentRecord.litigationCaseId);\n      _this8.getList();\n    }).catch(function (error) {\n      console.error('审批失败:', error);\n    });\n  }), \"handleBatchApprove\", function handleBatchApprove(action) {\n    if (this.selectedRecords.length === 0) {\n      this.$modal.msgError('请选择要审批的记录');\n      return;\n    }\n    this.batchApprovalForm.action = action;\n    this.batchApprovalForm.rejectReason = '';\n    this.batchApprovalDialogVisible = true;\n  }), \"confirmBatchApproval\", function confirmBatchApproval() {\n    var _this9 = this;\n    if (this.batchApprovalForm.action === 'reject') {\n      this.$refs[\"batchApprovalForm\"].validate(function (valid) {\n        if (!valid) return;\n        _this9.executeBatchApproval();\n      });\n    } else {\n      this.executeBatchApproval();\n    }\n  }), \"executeBatchApproval\", function executeBatchApproval() {\n    var _this0 = this;\n    var data = {\n      ids: this.selectedRecords.map(function (record) {\n        return record.id;\n      }),\n      status: this.batchApprovalForm.action === 'approve' ? '1' : '7',\n      rejectReason: this.batchApprovalForm.rejectReason\n    };\n    (0, _litigation_cost_approval.batchApproveLitigationCostRecords)(data).then(function () {\n      _this0.$modal.msgSuccess(\"\\u6279\\u91CF\".concat(_this0.batchApprovalForm.action === 'approve' ? '通过' : '拒绝', \"\\u5BA1\\u6279\\u6210\\u529F\"));\n      _this0.batchApprovalDialogVisible = false;\n      _this0.selectedRecords = [];\n      _this0.loadSubmissionRecords(_this0.currentRecord.litigationCaseId);\n      _this0.getList();\n    }).catch(function (error) {\n      console.error('批量审批失败:', error);\n    });\n  })\n};", "map": {"version": 3, "names": ["_litigation_cost_approval", "require", "_area", "_interopRequireDefault", "_userInfo", "_approvalStatus", "name", "components", "userInfo", "data", "_defineProperty2", "default", "loading", "ids", "single", "multiple", "showSearch", "total", "vm_car_orderList", "title", "open", "queryParams", "pageNum", "pageSize", "customerName", "certId", "jgName", "lendingBank", "litigationStatus", "applicationBy", "costCategory", "approvalStatus", "startTime", "endTime", "approvalStartTime", "approvalEndTime", "date<PERSON><PERSON><PERSON>", "approvalDateRange", "form", "id", "status", "rejectReason", "currentRecord", "submissionRecords", "recordsLoading", "selected<PERSON><PERSON><PERSON><PERSON>", "singleApprovalOpen", "singleApprovalForm", "batchApprovalOpen", "batchApprovalForm", "singleApprovalDialogVisible", "action", "value", "label", "children", "key<PERSON><PERSON>ince", "keyCity", "<PERSON><PERSON><PERSON><PERSON>", "keyDetailAddress", "areaList", "created", "getList", "methods", "_this", "listLitigationCostApproval", "then", "response", "rows", "cancel", "reset", "batchApprovalDialogVisible", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "loadSubmissionRecords", "litigationCaseId", "handleBatchEdit", "$modal", "msgError", "_this2", "getLitigationCostSubmissionRecords", "catch", "handleRecordSelectionChange", "handleSingleApprove", "record", "confirmSingleApproval", "_this3", "approveLitigationCostRecord", "msgSuccess", "handleBatchApprove", "confirmBatchApproval", "_this4", "batchApproveLitigationCostRecords", "handleBatchApproveMain", "_this5", "statusText", "confirm", "concat", "handleDelete", "_this6", "handleExport", "download", "_objectSpread2", "Date", "getTime", "openUserInfo", "customerId", "applyId", "customerInfo", "userInfoVisible", "handleDateRangeChange", "dates", "handleApprovalDateRangeChange", "getStatusText", "statusMap", "getStatusTagType", "canApproveRecord", "_this7", "$refs", "validate", "valid", "executeSingleApproval", "_this8", "error", "console", "_this9", "executeBatchApproval", "_this0"], "sources": ["src/views/litigation/litigation/litigation_approval.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <!-- 1. 贷款人姓名 -->\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"贷款人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 2. 贷款人身份证号 -->\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input\r\n          v-model=\"queryParams.certId\"\r\n          placeholder=\"贷款人身份证号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 3. 出单渠道 -->\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input\r\n          v-model=\"queryParams.jgName\"\r\n          placeholder=\"出单渠道\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 4. 放款银行 -->\r\n      <el-form-item label=\"\" prop=\"lendingBank\">\r\n        <el-input\r\n          v-model=\"queryParams.lendingBank\"\r\n          placeholder=\"放款银行\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 5. 法诉状态(需呈现多级) -->\r\n      <el-form-item label=\"\" prop=\"litigationStatus\">\r\n        <el-cascader\r\n          v-model=\"queryParams.litigationStatus\"\r\n          :options=\"litigationStatusOptions\"\r\n          :props=\"{ expandTrigger: 'hover', value: 'value', label: 'label', children: 'children' }\"\r\n          placeholder=\"法诉状态\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 6. 申请人 -->\r\n      <el-form-item label=\"\" prop=\"applicationBy\">\r\n        <el-input\r\n          v-model=\"queryParams.applicationBy\"\r\n          placeholder=\"申请人\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 7. 费用类型 -->\r\n      <el-form-item label=\"\" prop=\"costCategory\">\r\n        <el-select v-model=\"queryParams.costCategory\" placeholder=\"费用类型\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"律师费\" value=\"律师费\" />\r\n          <el-option label=\"诉讼费\" value=\"诉讼费\" />\r\n          <el-option label=\"保全费\" value=\"保全费\" />\r\n          <el-option label=\"执行费\" value=\"执行费\" />\r\n          <el-option label=\"其他费用\" value=\"其他费用\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 8. 审批状态 -->\r\n      <el-form-item label=\"\" prop=\"approvalStatus\">\r\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"审批状态\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"未审核\" value=\"\" />\r\n          <el-option label=\"已通过\" value=\"0\" />\r\n          <el-option label=\"已拒绝\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 9. 申请时间区间 -->\r\n      <el-form-item label=\"\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"申请开始日期\"\r\n          end-placeholder=\"申请结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 10. 审批时间区间 -->\r\n      <el-form-item label=\"\" prop=\"approvalDateRange\">\r\n        <el-date-picker\r\n          v-model=\"approvalDateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"审批开始日期\"\r\n          end-placeholder=\"审批结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleApprovalDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleBatchEdit\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vm_car_orderList\" @selection-change=\"handleSelectionChange\" row-key=\"id\" style=\"width: 100%\" flex=\"right\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"60\" fixed />\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n      <el-table-column label=\"最新申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"curator\" width=\"100\" />\r\n      <el-table-column label=\"提交次数\" align=\"center\" prop=\"submissionCount\" width=\"100\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"litigationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{scope.row.litigationStatus == '1'?'待立案':'已邮寄'}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贷款人\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"openUserInfo(scope.row)\"\r\n            style=\"color: #409EFF;\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" />\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"area\" width=\"80\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"lendingBank\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"courtLocation\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"commonPleas\" />\r\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n      <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n      <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n      <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n      <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n      <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n      <el-table-column label=\"特殊通道费\" align=\"center\" prop=\"specialChannelFees\" width=\"100\" />\r\n      <el-table-column label=\"日常报销\" align=\"center\" prop=\"otherAmountsOwed\" width=\"80\" />\r\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"overallApprovalStatus\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.approvalFlowStatus || 0)\">\r\n            {{ getStatusText(scope.row.approvalFlowStatus || 0) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n      <el-table-column label=\"审批人角色\" align=\"center\" prop=\"approveRole\" />\r\n      <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n          >审批</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 审批对话框 -->\r\n    <el-dialog title=\"法诉费用审批详情\" :visible.sync=\"open\" width=\"1200px\" append-to-body>\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>{{ currentRecord.customerName }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>案件负责人：</strong>{{ currentRecord.curator }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>法院地：</strong>{{ currentRecord.courtLocation }}\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('approve')\">\r\n          批量通过 ({{ selectedRecords.length }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('reject')\">\r\n          批量拒绝 ({{ selectedRecords.length }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"submissionRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n        <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n        <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n        <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n        <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n        <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n        <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n        <el-table-column label=\"违约金\" align=\"center\" prop=\"penalty\" width=\"80\" />\r\n        <el-table-column label=\"担保费\" align=\"center\" prop=\"guaranteeFee\" width=\"80\" />\r\n        <el-table-column label=\"居间费\" align=\"center\" prop=\"intermediaryFee\" width=\"80\" />\r\n        <el-table-column label=\"代偿金\" align=\"center\" prop=\"compensity\" width=\"80\" />\r\n        <el-table-column label=\"判决金额\" align=\"center\" prop=\"judgmentAmount\" width=\"100\" />\r\n        <el-table-column label=\"利息\" align=\"center\" prop=\"interest\" width=\"80\" />\r\n        <el-table-column label=\"其他欠款\" align=\"center\" prop=\"otherAmountsOwed\" width=\"100\" />\r\n        <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n        <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.approvalStatus == null || scope.row.approvalStatus == '' || scope.row.approvalStatus == '0'\" type=\"info\">未审核</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '1'\" type=\"success\">已通过</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '7'\" type=\"danger\">已拒绝</el-tag>\r\n            <el-tag v-else type=\"warning\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n        <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleSingleApprove(scope.row, 'approve')\">\r\n              通过\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleSingleApprove(scope.row, 'reject')\">\r\n              拒绝\r\n            </el-button>\r\n            <div v-else>\r\n              <el-tag v-if=\"scope.row.approvalStatus == '1'\" type=\"success\" size=\"mini\">已通过</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '7'\" type=\"danger\" size=\"mini\">已拒绝</el-tag>\r\n              <el-tag v-else type=\"info\" size=\"mini\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n              <div v-if=\"scope.row.approveBy\" style=\"font-size: 12px; color: #999; margin-top: 2px;\">\r\n                {{ scope.row.approveBy }}\r\n              </div>\r\n              <div v-if=\"scope.row.approveTime\" style=\"font-size: 12px; color: #999;\">\r\n                {{ scope.row.approveTime }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag v-if=\"singleApprovalForm.status == '0'\" type=\"success\">通过</el-tag>\r\n          <el-tag v-else type=\"danger\">拒绝</el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"拒绝原因\" v-if=\"singleApprovalForm.status == '1'\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n            v-model=\"singleApprovalForm.rejectReason\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag v-if=\"batchApprovalForm.status == '0'\" type=\"success\">批量通过</el-tag>\r\n          <el-tag v-else type=\"danger\">批量拒绝</el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item label=\"拒绝原因\" v-if=\"batchApprovalForm.status == '1'\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n            v-model=\"batchApprovalForm.rejectReason\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLitigationCostApproval,\r\n  getLitigationCostSubmissionRecords,\r\n  approveLitigationCostRecord,\r\n  batchApproveLitigationCostRecords,\r\n  startLitigationApprovalFlow,\r\n  approveLitigationCostFlow,\r\n  rejectLitigationCostFlow,\r\n  batchApproveLitigationCostFlow\r\n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\r\nimport areaList from \"../../../assets/area.json\"\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport ApprovalManager from \"@/utils/approvalStatus\"\r\nexport default {\r\n  name: \"Vm_car_order\",\r\n  components: {\r\n    userInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vm_car_orderList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        // 严格按照9个筛选条件\r\n        // 1. 贷款人姓名\r\n        customerName: null,\r\n        // 2. 贷款人身份证号\r\n        certId: null,\r\n        // 3. 出单渠道\r\n        jgName: null,\r\n        // 4. 放款银行\r\n        lendingBank: null,\r\n        // 5. 法诉状态(多级)\r\n        litigationStatus: null,\r\n        // 6. 申请人\r\n        applicationBy: null,\r\n        // 7. 费用类型\r\n        costCategory: null,\r\n        // 8. 审批状态\r\n        approvalStatus: null,\r\n        // 9. 申请时间区间\r\n        startTime: null,\r\n        endTime: null,\r\n        // 10. 审批时间区间\r\n        approvalStartTime: null,\r\n        approvalEndTime: null,\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 审批日期范围\r\n      approvalDateRange: [],\r\n      // 表单参数\r\n      form: {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      },\r\n      // 当前审批记录\r\n      currentRecord: {},\r\n      // 费用提交记录列表\r\n      submissionRecords: [],\r\n      // 记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalOpen: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        status: '0',\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalOpen: false,\r\n      batchApprovalForm: {\r\n        status: '0',\r\n        rejectReason: ''\r\n      },\r\n      // 单个审批对话框\r\n      singleApprovalDialogVisible: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalDialogVisible: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 贷款人信息相关\r\n      userInfoVisible: false,\r\n      customerInfo: {},\r\n      // 法诉状态多级选项\r\n      litigationStatusOptions: [\r\n        {\r\n          value: '起诉',\r\n          label: '起诉',\r\n          children: [\r\n            { value: '起诉-准备材料', label: '准备材料' },\r\n            { value: '起诉-已提交', label: '已提交' },\r\n            { value: '起诉-法院受理', label: '法院受理' }\r\n          ]\r\n        },\r\n        {\r\n          value: '审理',\r\n          label: '审理',\r\n          children: [\r\n            { value: '审理-开庭审理', label: '开庭审理' },\r\n            { value: '审理-等待判决', label: '等待判决' },\r\n            { value: '审理-一审判决', label: '一审判决' }\r\n          ]\r\n        },\r\n        {\r\n          value: '执行',\r\n          label: '执行',\r\n          children: [\r\n            { value: '执行-申请执行', label: '申请执行' },\r\n            { value: '执行-执行中', label: '执行中' },\r\n            { value: '执行-执行完毕', label: '执行完毕' }\r\n          ]\r\n        },\r\n        {\r\n          value: '结案',\r\n          label: '结案',\r\n          children: [\r\n            { value: '结案-胜诉结案', label: '胜诉结案' },\r\n            { value: '结案-败诉结案', label: '败诉结案' },\r\n            { value: '结案-和解结案', label: '和解结案' }\r\n          ]\r\n        }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        keyProvince:'',\r\n        keyCity:'',\r\n        keyBorough:'',\r\n        keyDetailAddress:'',\r\n      },\r\n      provinceList:areaList,\r\n      cityList:[],\r\n      districtList:[]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigationCostApproval(this.queryParams).then(response => {\r\n        this.vm_car_orderList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      }\r\n      this.currentRecord = {}\r\n      this.submissionRecords = []\r\n      this.selectedRecords = []\r\n      this.singleApprovalOpen = false\r\n      this.batchApprovalOpen = false\r\n      this.singleApprovalForm = {\r\n        id: '',\r\n        status: '0',\r\n        rejectReason: ''\r\n      }\r\n      this.batchApprovalForm = {\r\n        status: '0',\r\n        rejectReason: ''\r\n      }\r\n      this.singleApprovalDialogVisible = false\r\n      this.singleApprovalForm = {\r\n        id: '',\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.batchApprovalDialogVisible = false\r\n      this.batchApprovalForm = {\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.approvalDateRange = []\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加VIEW\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.currentRecord = row\r\n      this.loadSubmissionRecords(row.litigationCaseId)\r\n      this.open = true\r\n    },\r\n\r\n    /** 批量修改按钮操作 */\r\n    handleBatchEdit() {\r\n      this.$modal.msgError('请选择单条记录进行审批操作')\r\n    },\r\n\r\n    /** 加载费用提交记录 */\r\n    loadSubmissionRecords(litigationCaseId) {\r\n      this.recordsLoading = true\r\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\r\n        this.submissionRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(() => {\r\n        this.recordsLoading = false\r\n      })\r\n    },\r\n\r\n    /** 记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, status) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.status = status\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalOpen = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.status == '1' && !this.singleApprovalForm.rejectReason) {\r\n        this.$modal.msgError('请输入拒绝原因')\r\n        return\r\n      }\r\n\r\n      approveLitigationCostRecord(this.singleApprovalForm).then(() => {\r\n        this.$modal.msgSuccess('审批成功')\r\n        this.singleApprovalOpen = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList() // 刷新主列表\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(status) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.status = status\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalOpen = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.status == '1' && !this.batchApprovalForm.rejectReason) {\r\n        this.$modal.msgError('请输入拒绝原因')\r\n        return\r\n      }\r\n\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        status: this.batchApprovalForm.status,\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveLitigationCostRecords(data).then(() => {\r\n        this.$modal.msgSuccess('批量审批成功')\r\n        this.batchApprovalOpen = false\r\n        this.selectedRecords = []\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList() // 刷新主列表\r\n      })\r\n    },\r\n\r\n    /** 主列表批量审批 */\r\n    handleBatchApproveMain(status) {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      const statusText = status === '0' ? '通过' : '拒绝'\r\n      this.$modal.confirm(`确认要批量${statusText}选中的 ${this.ids.length} 条记录吗？`).then(() => {\r\n        const data = {\r\n          ids: this.ids,\r\n          status: status,\r\n          rejectReason: status === '1' ? '批量拒绝' : ''\r\n        }\r\n\r\n        return batchApproveLitigationCostRecords(data)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(`批量${statusText}成功`)\r\n        this.getList()\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete() {\r\n      this.$modal.confirm('是否确认删除选中的数据项？').then(() => {\r\n        // 这里可以调用删除API，暂时只是提示\r\n        this.$modal.msgSuccess(\"删除功能暂未实现\")\r\n      }).catch(() => {})\r\n    },\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\r\n        ...this.queryParams\r\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 打开贷款人信息 */\r\n    openUserInfo(row) {\r\n      if (!row.customerId && !row.applyId) {\r\n        this.$modal.msgError('无法获取贷款人信息')\r\n        return\r\n      }\r\n\r\n      this.customerInfo = {\r\n        customerId: row.customerId,\r\n        applyId: row.applyId,\r\n        customerName: row.customerName\r\n      }\r\n      this.userInfoVisible = true\r\n    },\r\n\r\n    /** 处理日期范围变化 */\r\n    handleDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.startTime = dates[0]\r\n        this.queryParams.endTime = dates[1]\r\n      } else {\r\n        this.queryParams.startTime = null\r\n        this.queryParams.endTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 处理审批时间范围变化 */\r\n    handleApprovalDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.approvalStartTime = dates[0]\r\n        this.queryParams.approvalEndTime = dates[1]\r\n      } else {\r\n        this.queryParams.approvalStartTime = null\r\n        this.queryParams.approvalEndTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '未审批',\r\n        1: '全部通过',\r\n        3: '法诉主管审批',\r\n        4: '总监审批',\r\n        5: '总监抄送',\r\n        6: '总经理/董事长审批(抄送)',\r\n        7: '已拒绝'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      switch (status) {\r\n        case 0:\r\n          return 'info'\r\n        case 1:\r\n          return 'success'\r\n        case 7:\r\n          return 'danger'\r\n        case 3:\r\n        case 4:\r\n        case 5:\r\n        case 6:\r\n          return 'warning'\r\n        default:\r\n          return 'info'\r\n      }\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 只有未审批的记录可以审批\r\n      return record.approvalStatus === '0' || record.approvalStatus === null || record.approvalStatus === ''\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        status: this.singleApprovalForm.action === 'approve' ? '1' : '7',\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      approveLitigationCostRecord(data).then(() => {\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalDialogVisible = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        status: this.batchApprovalForm.action === 'approve' ? '1' : '7',\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveLitigationCostRecords(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalDialogVisible = false\r\n        this.selectedRecords = []\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('批量审批失败:', error)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA6bA,IAAAA,yBAAA,GAAAC,OAAA;AAUA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,eAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAK,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,gBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACA;QACA;QACAC,YAAA;QACA;QACAC,MAAA;QACA;QACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,gBAAA;QACA;QACAC,aAAA;QACA;QACAC,YAAA;QACA;QACAC,cAAA;QACA;QACAC,SAAA;QACAC,OAAA;QACA;QACAC,iBAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;MACA;MACAC,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACA;MACAC,aAAA;MACA;MACAC,iBAAA;MACA;MACAC,cAAA;MACA;MACAC,eAAA;MACA;MACAC,kBAAA;MACAC,kBAAA;QACAR,EAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACA;MACAO,iBAAA;MACAC,iBAAA;QACAT,MAAA;QACAC,YAAA;MACA;MACA;MACAS,2BAAA;IAAA,yBACA;MACAX,EAAA;MACAY,MAAA;MAAA;MACAV,YAAA;IACA,kCAEA,6BACA;MACAU,MAAA;MAAA;MACAV,YAAA;IACA,uBAEA,wBACA,gCAEA,CACA;MACAW,KAAA;MACAC,KAAA;MACAC,QAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA,GACA;MACAD,KAAA;MACAC,KAAA;MACAC,QAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA,GACA;MACAD,KAAA;MACAC,KAAA;MACAC,QAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA,GACA;MACAD,KAAA;MACAC,KAAA;MACAC,QAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA,EACA,YAEA;MACAE,WAAA;MACAC,OAAA;MACAC,UAAA;MACAC,gBAAA;IACA,oBACAC,aAAA,eACA,qBACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA,MAAApD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;IACA,iBACAkD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAnD,OAAA;MACA,IAAAoD,oDAAA,OAAA3C,WAAA,EAAA4C,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA7C,gBAAA,GAAAgD,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA9C,KAAA,GAAAiD,QAAA,CAAAjD,KAAA;QACA8C,KAAA,CAAAnD,OAAA;MACA;IACA;IACA;IACAwD,MAAA,WAAAA,OAAA;MACA,KAAAhD,IAAA;MACA,KAAAiD,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA/B,IAAA;QACAC,EAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACA,KAAAC,aAAA;MACA,KAAAC,iBAAA;MACA,KAAAE,eAAA;MACA,KAAAC,kBAAA;MACA,KAAAE,iBAAA;MACA,KAAAD,kBAAA;QACAR,EAAA;QACAC,MAAA;QACAC,YAAA;MACA;MACA,KAAAQ,iBAAA;QACAT,MAAA;QACAC,YAAA;MACA;MACA,KAAAS,2BAAA;MACA,KAAAH,kBAAA;QACAR,EAAA;QACAY,MAAA;QACAV,YAAA;MACA;MACA,KAAA6B,0BAAA;MACA,KAAArB,iBAAA;QACAE,MAAA;QACAV,YAAA;MACA;MACA,KAAA8B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAnD,WAAA,CAAAC,OAAA;MACA,KAAAuC,OAAA;IACA;IACA,aACAY,UAAA,WAAAA,WAAA;MACA,KAAArC,SAAA;MACA,KAAAC,iBAAA;MACA,KAAAkC,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9D,GAAA,GAAA8D,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAtC,EAAA;MAAA;MACA,KAAAzB,MAAA,GAAA6D,SAAA,CAAAG,MAAA;MACA,KAAA/D,QAAA,IAAA4D,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA;MACA,KAAAjD,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA6D,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAvC,aAAA,GAAAuC,GAAA;MACA,KAAAC,qBAAA,CAAAD,GAAA,CAAAE,gBAAA;MACA,KAAA/D,IAAA;IACA;IAEA,eACAgE,eAAA,WAAAA,gBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;IACA;IAEA,eACAJ,qBAAA,WAAAA,sBAAAC,gBAAA;MAAA,IAAAI,MAAA;MACA,KAAA3C,cAAA;MACA,IAAA4C,4DAAA,EAAAL,gBAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAA5C,iBAAA,GAAAuB,QAAA,CAAAzD,IAAA;QACA8E,MAAA,CAAA3C,cAAA;MACA,GAAA6C,KAAA;QACAF,MAAA,CAAA3C,cAAA;MACA;IACA;IAEA,aACA8C,2BAAA,WAAAA,4BAAAf,SAAA;MACA,KAAA9B,eAAA,GAAA8B,SAAA;IACA;IAEA,WACAgB,mBAAA,WAAAA,oBAAAC,MAAA,EAAApD,MAAA;MACA,KAAAO,kBAAA,CAAAR,EAAA,GAAAqD,MAAA,CAAArD,EAAA;MACA,KAAAQ,kBAAA,CAAAP,MAAA,GAAAA,MAAA;MACA,KAAAO,kBAAA,CAAAN,YAAA;MACA,KAAAK,kBAAA;IACA;IAEA,aACA+C,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,SAAA/C,kBAAA,CAAAP,MAAA,iBAAAO,kBAAA,CAAAN,YAAA;QACA,KAAA4C,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,IAAAS,qDAAA,OAAAhD,kBAAA,EAAAkB,IAAA;QACA6B,MAAA,CAAAT,MAAA,CAAAW,UAAA;QACAF,MAAA,CAAAhD,kBAAA;QACAgD,MAAA,CAAAZ,qBAAA,CAAAY,MAAA,CAAApD,aAAA,CAAAyC,gBAAA;QACAW,MAAA,CAAAjC,OAAA;MACA;IACA;IAEA,WACAoC,kBAAA,WAAAA,mBAAAzD,MAAA;MACA,SAAAK,eAAA,CAAAiC,MAAA;QACA,KAAAO,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAArC,iBAAA,CAAAT,MAAA,GAAAA,MAAA;MACA,KAAAS,iBAAA,CAAAR,YAAA;MACA,KAAAO,iBAAA;IACA;IAEA,aACAkD,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,SAAAlD,iBAAA,CAAAT,MAAA,iBAAAS,iBAAA,CAAAR,YAAA;QACA,KAAA4C,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,IAAA7E,IAAA;QACAI,GAAA,OAAAgC,eAAA,CAAA+B,GAAA,WAAAgB,MAAA;UAAA,OAAAA,MAAA,CAAArD,EAAA;QAAA;QACAC,MAAA,OAAAS,iBAAA,CAAAT,MAAA;QACAC,YAAA,OAAAQ,iBAAA,CAAAR;MACA;MAEA,IAAA2D,2DAAA,EAAA3F,IAAA,EAAAwD,IAAA;QACAkC,MAAA,CAAAd,MAAA,CAAAW,UAAA;QACAG,MAAA,CAAAnD,iBAAA;QACAmD,MAAA,CAAAtD,eAAA;QACAsD,MAAA,CAAAjB,qBAAA,CAAAiB,MAAA,CAAAzD,aAAA,CAAAyC,gBAAA;QACAgB,MAAA,CAAAtC,OAAA;MACA;IACA;IAEA,cACAwC,sBAAA,WAAAA,uBAAA7D,MAAA;MAAA,IAAA8D,MAAA;MACA,SAAAzF,GAAA,CAAAiE,MAAA;QACA,KAAAO,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,IAAAiB,UAAA,GAAA/D,MAAA;MACA,KAAA6C,MAAA,CAAAmB,OAAA,kCAAAC,MAAA,CAAAF,UAAA,yBAAAE,MAAA,MAAA5F,GAAA,CAAAiE,MAAA,sCAAAb,IAAA;QACA,IAAAxD,IAAA;UACAI,GAAA,EAAAyF,MAAA,CAAAzF,GAAA;UACA2B,MAAA,EAAAA,MAAA;UACAC,YAAA,EAAAD,MAAA;QACA;QAEA,WAAA4D,2DAAA,EAAA3F,IAAA;MACA,GAAAwD,IAAA;QACAqC,MAAA,CAAAjB,MAAA,CAAAW,UAAA,gBAAAS,MAAA,CAAAF,UAAA;QACAD,MAAA,CAAAzC,OAAA;MACA,GAAA4B,KAAA;IACA;IAEA,aACAiB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAtB,MAAA,CAAAmB,OAAA,kBAAAvC,IAAA;QACA;QACA0C,MAAA,CAAAtB,MAAA,CAAAW,UAAA;MACA,GAAAP,KAAA;IACA;IAGA,aACAmB,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iEAAAC,cAAA,CAAAnG,OAAA,MACA,KAAAU,WAAA,+BAAAoF,MAAA,CACA,IAAAM,IAAA,GAAAC,OAAA;IACA;IAEA,cACAC,YAAA,WAAAA,aAAAhC,GAAA;MACA,KAAAA,GAAA,CAAAiC,UAAA,KAAAjC,GAAA,CAAAkC,OAAA;QACA,KAAA9B,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,KAAA8B,YAAA;QACAF,UAAA,EAAAjC,GAAA,CAAAiC,UAAA;QACAC,OAAA,EAAAlC,GAAA,CAAAkC,OAAA;QACA3F,YAAA,EAAAyD,GAAA,CAAAzD;MACA;MACA,KAAA6F,eAAA;IACA;IAEA,eACAC,qBAAA,WAAAA,sBAAAC,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAzC,MAAA;QACA,KAAAzD,WAAA,CAAAW,SAAA,GAAAuF,KAAA;QACA,KAAAlG,WAAA,CAAAY,OAAA,GAAAsF,KAAA;MACA;QACA,KAAAlG,WAAA,CAAAW,SAAA;QACA,KAAAX,WAAA,CAAAY,OAAA;MACA;MACA,KAAAuC,WAAA;IACA;IAEA,iBACAgD,6BAAA,WAAAA,8BAAAD,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAzC,MAAA;QACA,KAAAzD,WAAA,CAAAa,iBAAA,GAAAqF,KAAA;QACA,KAAAlG,WAAA,CAAAc,eAAA,GAAAoF,KAAA;MACA;QACA,KAAAlG,WAAA,CAAAa,iBAAA;QACA,KAAAb,WAAA,CAAAc,eAAA;MACA;MACA,KAAAqC,WAAA;IACA;IAEA,aACAiD,aAAA,WAAAA,cAAAjF,MAAA;MACA,IAAAkF,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAlF,MAAA;IACA;IAEA,eACAmF,gBAAA,WAAAA,iBAAAnF,MAAA;MACA,QAAAA,MAAA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA,iBACAoF,gBAAA,WAAAA,iBAAAhC,MAAA;MACA;MACA,OAAAA,MAAA,CAAA7D,cAAA,YAAA6D,MAAA,CAAA7D,cAAA,aAAA6D,MAAA,CAAA7D,cAAA;IACA;EAAA,mCAAA4D,oBAGAC,MAAA,EAAAzC,MAAA;IACA,KAAAJ,kBAAA,CAAAR,EAAA,GAAAqD,MAAA,CAAArD,EAAA;IACA,KAAAQ,kBAAA,CAAAI,MAAA,GAAAA,MAAA;IACA,KAAAJ,kBAAA,CAAAN,YAAA;IACA,KAAAS,2BAAA;EACA,sCAAA2C,sBAAA,EAGA;IAAA,IAAAgC,MAAA;IACA,SAAA9E,kBAAA,CAAAI,MAAA;MACA,KAAA2E,KAAA,uBAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAH,MAAA,CAAAI,qBAAA;MACA;IACA;MACA,KAAAA,qBAAA;IACA;EACA,sCAGAA,sBAAA;IAAA,IAAAC,MAAA;IACA,IAAAzH,IAAA;MACA8B,EAAA,OAAAQ,kBAAA,CAAAR,EAAA;MACAC,MAAA,OAAAO,kBAAA,CAAAI,MAAA;MACAV,YAAA,OAAAM,kBAAA,CAAAN;IACA;IAEA,IAAAsD,qDAAA,EAAAtF,IAAA,EAAAwD,IAAA;MACAiE,MAAA,CAAA7C,MAAA,CAAAW,UAAA,IAAAS,MAAA,CAAAyB,MAAA,CAAAnF,kBAAA,CAAAI,MAAA;MACA+E,MAAA,CAAAhF,2BAAA;MACAgF,MAAA,CAAAhD,qBAAA,CAAAgD,MAAA,CAAAxF,aAAA,CAAAyC,gBAAA;MACA+C,MAAA,CAAArE,OAAA;IACA,GAAA4B,KAAA,WAAA0C,KAAA;MACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;IACA;EACA,mCAAAlC,mBAGA9C,MAAA;IACA,SAAAN,eAAA,CAAAiC,MAAA;MACA,KAAAO,MAAA,CAAAC,QAAA;MACA;IACA;IAEA,KAAArC,iBAAA,CAAAE,MAAA,GAAAA,MAAA;IACA,KAAAF,iBAAA,CAAAR,YAAA;IACA,KAAA6B,0BAAA;EACA,qCAAA4B,qBAAA,EAGA;IAAA,IAAAmC,MAAA;IACA,SAAApF,iBAAA,CAAAE,MAAA;MACA,KAAA2E,KAAA,sBAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAK,MAAA,CAAAC,oBAAA;MACA;IACA;MACA,KAAAA,oBAAA;IACA;EACA,qCAGAA,qBAAA;IAAA,IAAAC,MAAA;IACA,IAAA9H,IAAA;MACAI,GAAA,OAAAgC,eAAA,CAAA+B,GAAA,WAAAgB,MAAA;QAAA,OAAAA,MAAA,CAAArD,EAAA;MAAA;MACAC,MAAA,OAAAS,iBAAA,CAAAE,MAAA;MACAV,YAAA,OAAAQ,iBAAA,CAAAR;IACA;IAEA,IAAA2D,2DAAA,EAAA3F,IAAA,EAAAwD,IAAA;MACAsE,MAAA,CAAAlD,MAAA,CAAAW,UAAA,gBAAAS,MAAA,CAAA8B,MAAA,CAAAtF,iBAAA,CAAAE,MAAA;MACAoF,MAAA,CAAAjE,0BAAA;MACAiE,MAAA,CAAA1F,eAAA;MACA0F,MAAA,CAAArD,qBAAA,CAAAqD,MAAA,CAAA7F,aAAA,CAAAyC,gBAAA;MACAoD,MAAA,CAAA1E,OAAA;IACA,GAAA4B,KAAA,WAAA0C,KAAA;MACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;IACA;EACA;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}