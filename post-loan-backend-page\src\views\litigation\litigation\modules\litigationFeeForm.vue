<template>
  <div>
    <!-- 法诉费用提交弹窗 -->
    <el-dialog title="提交法诉费用" :visible.sync="dialogVisible" width="800px" append-to-body>
    <el-descriptions :column="1" border style="margin-bottom: 20px">
      <el-descriptions-item label="贷款人">{{ litigationFee.贷款人 }}</el-descriptions-item>
      <el-descriptions-item label="出单渠道">{{ litigationFee.出单渠道 }}</el-descriptions-item>
      <el-descriptions-item label="放款银行">{{ litigationFee.放款银行 }}</el-descriptions-item>
    </el-descriptions>

    <div style="padding: 0 20px">
      <!-- 费用提交规则提示 -->
      <div class="fee-rules-container">
        <div class="fee-rules-header">
          <i class="el-icon-info"></i>
          <span class="fee-rules-title">费用提交规则</span>
        </div>
        <div class="fee-rules-content">
          <div class="rule-item">
            <span class="rule-bullet">•</span>
            <span><strong>判决金额</strong>和<strong>利息</strong>每个案件只能提交一次</span>
          </div>
          <div class="rule-item">
            <span class="rule-bullet">•</span>
            <span><strong>日常费用</strong>（如油费、路费、餐费等）请在"日常费用审批"模块中提交</span>
          </div>
          <div class="rule-item">
            <span class="rule-bullet">•</span>
            <span>其他诉讼费用可以多次提交</span>
          </div>
        </div>
      </div>

      <div style="margin-bottom: 20px">
        <div style="display: flex; align-items: flex-start">
          <label style="width: 120px; text-align: right; margin-right: 10px; font-weight: bold; line-height: 32px; flex-shrink: 0">法诉费用：</label>
          <div style="flex: 1">
            <div v-for="(item, index) in litigationFees" :key="index" style="margin-bottom: 10px">
              <el-row :gutter="10" type="flex" align="middle">
                <el-col :span="8">
                  <el-select
                    v-model="item.type"
                    placeholder="请选择法诉费用类型"
                    style="width: 100%"
                    @change="handleLitigationFeeTypeChange(item, index)">
                    <el-option
                      v-for="option in litigationFeeTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                      :disabled="isTypeDisabled(option.value, index)" />
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-input
                    v-model="item.amount"
                    placeholder="请输入金额"
                    @input="handleLitigationFeeAmountChange"
                    :disabled="item.type === ''">
                    <template slot="prepend">￥</template>
                  </el-input>
                </el-col>
                <el-col :span="4">
                  <el-button type="danger" icon="el-icon-delete" style="width: 54px" @click="removeLitigationFee(index)" />
                </el-col>
              </el-row>
            </div>

            <!-- 新增按钮 -->
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addLitigationFee">新增法诉费用</el-button>
            <el-button type="success" size="small" icon="el-icon-document" @click="openDailyExpenseDialog" style="margin-left: 10px">申请日常费用</el-button>
            <el-button type="info" size="small" icon="el-icon-view" @click="viewDailyExpenseList" style="margin-left: 10px">查看日常费用</el-button>
          </div>
        </div>
      </div>

      <div style="margin-bottom: 20px">
        <label style="display: inline-block; width: 120px; text-align: right; margin-right: 10px; font-weight: bold">合计法诉费用：</label>
        <div style="display: inline-block; width: calc(100% - 130px)">
          <el-input v-model="litigationFee.totalMoney" placeholder="请输入合计法诉费用" disabled>
            <template slot="prepend">￥</template>
          </el-input>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>

  <!-- 日常费用申请弹窗 -->
  <el-dialog title="申请日常费用" :visible.sync="dailyExpenseDialogVisible" width="600px" append-to-body>
    <el-form ref="dailyExpenseForm" :model="dailyExpenseForm" :rules="dailyExpenseRules" label-width="120px">
      <el-form-item label="费用类型" prop="expenseType">
        <el-select v-model="dailyExpenseForm.expenseType" placeholder="请选择费用类型" style="width: 100%">
          <el-option label="油费" value="oil_fee" />
          <el-option label="路费" value="road_fee" />
          <el-option label="餐费" value="meal_fee" />
          <el-option label="住宿费" value="accommodation_fee" />
          <el-option label="交通费" value="transport_fee" />
          <el-option label="停车费" value="parking_fee" />
          <el-option label="通讯费" value="communication_fee" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="费用金额" prop="expenseAmount">
        <el-input v-model="dailyExpenseForm.expenseAmount" placeholder="请输入费用金额">
          <template slot="prepend">￥</template>
        </el-input>
      </el-form-item>
      <el-form-item label="费用发生日期" prop="expenseDate">
        <el-date-picker
          v-model="dailyExpenseForm.expenseDate"
          type="date"
          placeholder="请选择费用发生日期"
          value-format="yyyy-MM-dd"
          style="width: 100%">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="费用说明" prop="expenseDescription">
        <el-input v-model="dailyExpenseForm.expenseDescription" type="textarea" placeholder="请输入费用说明" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancelDailyExpense">取 消</el-button>
      <el-button type="primary" @click="submitDailyExpense">确 定</el-button>
    </div>
  </el-dialog>

  <!-- 查看日常费用列表弹窗 -->
  <el-dialog title="日常费用申请记录" :visible.sync="dailyExpenseListDialogVisible" width="800px" append-to-body>
    <el-table :data="dailyExpenseList" v-loading="dailyExpenseListLoading">
      <el-table-column label="费用类型" align="center">
        <template slot-scope="scope">
          {{ getExpenseTypeLabel(scope.row.expenseType) }}
        </template>
      </el-table-column>
      <el-table-column label="费用金额" align="center">
        <template slot-scope="scope">
          ￥{{ scope.row.expenseAmount }}
        </template>
      </el-table-column>
      <el-table-column label="费用发生日期" align="center">
        <template slot-scope="scope">
          {{ scope.row.expenseDate }}
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.applicationTime }}
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.approvalStatus)">
            {{ getStatusText(scope.row.approvalStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="费用说明" align="center" prop="expenseDescription" />
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dailyExpenseListDialogVisible = false">关 闭</el-button>
    </div>
  </el-dialog>
  </div>
</template>

<style scoped>
.fee-rules-container {
  background-color: #f4f4f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
}

.fee-rules-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.fee-rules-header .el-icon-info {
  color: #409EFF;
  font-size: 16px;
  margin-right: 8px;
}

.fee-rules-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.fee-rules-content {
  padding-left: 24px;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  line-height: 1.5;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.rule-bullet {
  color: #409EFF;
  margin-right: 8px;
  font-weight: bold;
}

.rule-item span {
  color: #606266;
  font-size: 13px;
}

.rule-item strong {
  color: #409EFF;
  font-weight: bold;
}
</style>

<script>
import { addLitigation_cost, checkSubmittedLimitedFees } from '@/api/litigation/litigation'
import { addDaily_expense_approval, listDaily_expense_approval } from '@/api/daily_expense_approval/daily_expense_approval'

export default {
  name: 'LitigationFeeForm',
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    data: {
      handler(newVal) {
        if (newVal) {
          const caseId = newVal.序号

          this.litigationFee = {
            litigationCaseId: caseId ? Number(caseId) : null,
            贷款人: String(newVal.贷款人 || ''),
            出单渠道: String(newVal.出单渠道 || ''),
            放款银行: String(newVal.放款银行 || ''),
            totalMoney: 0,
          }
          this.litigationFees = []
          // 检查已提交的限制性费用类型
          if (caseId) {
            this.checkSubmittedLimitedTypes(caseId)
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
      litigationFee: {
        litigationCaseId: '',
        贷款人: '',
        出单渠道: '',
        放款银行: '',
        totalMoney: 0,
      }, // 法诉费用提交数据
      litigationFees: [], // 记录费用的列表
      litigationFeeTypeOptions: [
        { label: '律师费', value: 'lawyerFee', category: 'litigation_fees', multiple: true },
        { label: '法诉费', value: 'litigationFee', category: 'litigation_fees', multiple: true },
        { label: '保全费', value: 'preservationFee', category: 'litigation_fees', multiple: true },
        { label: '布控费', value: 'surveillanceFee', category: 'litigation_fees', multiple: true },
        { label: '公告费', value: 'announcementFee', category: 'litigation_fees', multiple: true },
        { label: '评估费', value: 'appraisalFee', category: 'litigation_fees', multiple: true },
        { label: '执行费', value: 'executionFee', category: 'litigation_fees', multiple: true },
        { label: '违约金', value: 'penalty', category: 'litigation_fees', multiple: true },
        { label: '担保费', value: 'guaranteeFee', category: 'litigation_fees', multiple: true },
        { label: '居间费', value: 'intermediaryFee', category: 'litigation_fees', multiple: true },
        { label: '代偿金', value: 'compensity', category: 'litigation_fees', multiple: true },
        { label: '判决金额', value: 'judgmentAmount', category: 'judgment_interest', multiple: false },
        { label: '利息', value: 'interest', category: 'judgment_interest', multiple: false },
        { label: '其他欠款', value: 'otherAmountsOwed', category: 'litigation_fees', multiple: true },
        { label: '保险费', value: 'insurance', category: 'litigation_fees', multiple: true },
      ],
      // 已提交的限制性费用类型
      submittedLimitedTypes: [],

      // 日常费用申请相关
      dailyExpenseDialogVisible: false,
      dailyExpenseForm: {
        litigationCaseId: '',
        loanId: null,
        status: 1, // 法诉提交
        expenseType: '',
        expenseAmount: '',
        expenseDate: '',
        expenseDescription: '',
        applicantId: '',
        applicantName: '',
        applicationTime: '',
        approvalStatus: '0'
      },
      dailyExpenseRules: {
        expenseType: [
          { required: true, message: '请选择费用类型', trigger: 'change' }
        ],
        expenseAmount: [
          { required: true, message: '请输入费用金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        expenseDate: [
          { required: true, message: '请选择费用发生日期', trigger: 'change' }
        ]
      },

      // 日常费用列表弹窗
      dailyExpenseListDialogVisible: false,
      dailyExpenseList: [],
      dailyExpenseListLoading: false
    }
  },
  methods: {
    // 新增法诉费用行
    addLitigationFee() {
      this.litigationFees.push({
        type: '',
        amount: '',
      })
    },

    // 判断某个类型是否已被其它行占用或者是限制性类型且已提交
    isTypeDisabled(type, currentIndex) {
      // 检查是否在当前表单中重复
      const isDuplicateInForm = this.litigationFees.some((fee, idx) => fee.type === type && idx !== currentIndex)

      // 检查是否是限制性类型且已提交过
      const typeOption = this.litigationFeeTypeOptions.find(option => option.value === type)
      const isLimitedAndSubmitted = typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(type)

      return isDuplicateInForm || isLimitedAndSubmitted
    },

    // 选中后校验：若重复或限制性类型已提交则清空
    handleLitigationFeeTypeChange(item, index) {
      const duplicate = this.litigationFees.some((fee, idx) => fee.type === item.type && idx !== index)
      if (duplicate) {
        this.$message.warning('该费用类型已选择，请勿重复！')
        this.$set(this.litigationFees[index], 'type', '')
        return
      }

      // 检查是否是限制性类型且已提交
      const typeOption = this.litigationFeeTypeOptions.find(option => option.value === item.type)
      if (typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(item.type)) {
        this.$message.warning(`${typeOption.label}只能提交一次，已经提交过了！`)
        this.$set(this.litigationFees[index], 'type', '')
        return
      }

      // 如果是日常报销类型，提示用户应该在日常费用审批中提交
      if (item.type === 'dailyReimbursement') {
        this.$message.warning('日常报销费用请在"日常费用审批"模块中提交！')
        this.$set(this.litigationFees[index], 'type', '')
        return
      }
    },

    // 金额变化时重新计算总金额
    handleLitigationFeeAmountChange() {
      this.litigationFee.totalMoney = this.litigationFees.reduce((sum, fee) => sum + Number(fee.amount || 0), 0)
    },

    // 删除法诉费用行
    removeLitigationFee(index) {
      this.litigationFees.splice(index, 1)
      this.handleLitigationFeeAmountChange()
    },

    submitForm() {
      // 验证是否有费用项目
      if (this.litigationFees.length === 0) {
        this.$message.warning('请至少添加一项费用！')
        return
      }

      // 验证所有费用项目是否完整
      const hasIncompleteItem = this.litigationFees.some(fee => !fee.type || !fee.amount)
      if (hasIncompleteItem) {
        this.$message.warning('请完善所有费用项目的类型和金额！')
        return
      }

      // 分离日常费用和其他费用
      const dailyExpenses = this.litigationFees.filter(fee => fee.type === 'dailyReimbursement')
      const otherFees = this.litigationFees.filter(fee => fee.type !== 'dailyReimbursement')

      // 如果有日常费用，提示用户
      if (dailyExpenses.length > 0) {
        this.$message.warning('检测到日常报销费用，请在"日常费用审批"模块中提交！')
        return
      }

      // 构建提交数据
      const litigationFee = { ...this.litigationFee }

      // 确保 litigationCaseId 是数字类型
      if (litigationFee.litigationCaseId) {
        litigationFee.litigationCaseId = Number(litigationFee.litigationCaseId)
      } else {
        this.$message.error('缺少法诉案件ID，请重新选择案件')
        return
      }

      // 将费用列表中的数据动态填入到 litigationFee 对象中
      otherFees.forEach(({ type, amount }) => {
        if (type) litigationFee[type] = amount || 0
      })



      // 调用 API 提交数据
      addLitigation_cost(litigationFee).then(res => {
        if (res.code === 200) {
          this.$message.success('提交成功')
          this.dialogVisible = false
          this.reset()
          // 刷新父组件数据
          this.$emit('refresh')
        } else {
          this.$message.error('提交失败：' + (res.msg || '未知错误'))
        }
      }).catch(error => {
        console.error('提交失败:', error)
        this.$message.error('提交失败，请稍后重试')
      })
    },

    cancel() {
      this.dialogVisible = false
      this.reset()
    },

    open() {
      this.reset()
      this.dialogVisible = true
    },

    // 检查已提交的限制性费用类型
    checkSubmittedLimitedTypes(litigationCaseId) {
      if (!litigationCaseId) return

      // 调用后端API检查已提交的判决金额和利息
      checkSubmittedLimitedFees(litigationCaseId).then(res => {
        if (res.code === 200) {
          this.submittedLimitedTypes = res.data || []
        }
      }).catch(error => {
        console.error('检查限制性费用类型失败:', error)
        this.submittedLimitedTypes = []
      })
    },

    // 打开日常费用申请弹窗
    openDailyExpenseDialog(litigationCaseId = null) {
      this.resetDailyExpenseForm()
      this.dailyExpenseForm.litigationCaseId = String(litigationCaseId || this.litigationFee.litigationCaseId || '')
      this.dailyExpenseForm.status = 1 // 法诉提交
      this.dailyExpenseForm.applicantId = String(this.$store.state.user?.id || '')
      this.dailyExpenseForm.applicantName = String(this.$store.state.user?.name || '')
      this.dailyExpenseForm.applicationTime = new Date().toISOString().split('T')[0]
      this.dailyExpenseDialogVisible = true
    },

    // 取消日常费用申请
    cancelDailyExpense() {
      this.dailyExpenseDialogVisible = false
      this.resetDailyExpenseForm()
    },

    // 提交日常费用申请
    submitDailyExpense() {
      this.$refs.dailyExpenseForm.validate((valid) => {
        if (valid) {
          // 法诉提交：确保有 litigationCaseId 和 status = 1
          const submitData = { ...this.dailyExpenseForm }
          if (!submitData.litigationCaseId) {
            this.$message.error('缺少法诉案件ID，请重新选择案件')
            return
          }
          submitData.status = 1 // 确保是法诉提交
          addDaily_expense_approval(submitData).then(res => {
            if (res.code === 200) {
              this.$message.success('日常费用申请提交成功')
              this.dailyExpenseDialogVisible = false
              this.resetDailyExpenseForm()
            } else {
              this.$message.error('提交失败：' + (res.msg || '未知错误'))
            }
          }).catch(error => {
            console.error('提交失败:', error)
            this.$message.error('提交失败，请稍后重试')
          })
        }
      })
    },



    // 重置日常费用表单
    resetDailyExpenseForm() {
      this.dailyExpenseForm = {
        litigationCaseId: '',
        loanId: null,
        status: 1, // 法诉提交
        expenseType: '',
        expenseAmount: '',
        expenseDate: '',
        expenseDescription: '',
        applicantId: '',
        applicantName: '',
        applicationTime: '',
        approvalStatus: '0'
      }
      if (this.$refs.dailyExpenseForm) {
        this.$refs.dailyExpenseForm.resetFields()
      }
    },

    reset() {
      // 保留 litigationCaseId，只重置其他字段
      const currentLitigationCaseId = this.litigationFee.litigationCaseId
      this.litigationFee = {
        litigationCaseId: currentLitigationCaseId,
        贷款人: this.litigationFee.贷款人,
        出单渠道: this.litigationFee.出单渠道,
        放款银行: this.litigationFee.放款银行,
        totalMoney: 0,
      }
      this.litigationFees = []
      // 不重置 submittedLimitedTypes，因为这是从后端获取的状态
    },

    // 查看日常费用列表
    viewDailyExpenseList() {
      this.dailyExpenseListLoading = true
      this.dailyExpenseListDialogVisible = true

      const queryParams = {
        litigationCaseId: this.litigationFee.litigationCaseId,
        pageNum: 1,
        pageSize: 100
      }

      listDaily_expense_approval(queryParams).then(res => {
        if (res.code === 200) {
          this.dailyExpenseList = res.rows || []
        } else {
          this.$message.error('获取日常费用列表失败：' + (res.msg || '未知错误'))
          this.dailyExpenseList = []
        }
        this.dailyExpenseListLoading = false
      }).catch(error => {
        console.error('获取日常费用列表失败:', error)
        this.$message.error('获取日常费用列表失败，请稍后重试')
        this.dailyExpenseList = []
        this.dailyExpenseListLoading = false
      })
    },

    // 获取费用类型标签
    getExpenseTypeLabel(type) {
      const typeMap = {
        'oil_fee': '油费',
        'road_fee': '路费',
        'meal_fee': '餐费',
        'accommodation_fee': '住宿费',
        'transport_fee': '交通费',
        'parking_fee': '停车费',
        'communication_fee': '通讯费',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    // 获取审批状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '待审批',
        '1': '全部通过',
        '2': '已拒绝',
        '3': '主管审批中',
        '4': '总监审批中',
        '5': '财务主管审批中',
        '6': '总经理审批中'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取审批状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        '0': 'warning',    // 待审批 - 橙色
        '1': 'success',    // 全部通过 - 绿色
        '2': 'danger',     // 已拒绝 - 红色
        '3': 'primary',    // 主管审批中 - 蓝色
        '4': 'primary',    // 总监审批中 - 蓝色
        '5': 'primary',    // 财务主管审批中 - 蓝色
        '6': 'primary'     // 总经理审批中 - 蓝色
      }
      return typeMap[status] || 'info'
    }
  },
}
</script>
