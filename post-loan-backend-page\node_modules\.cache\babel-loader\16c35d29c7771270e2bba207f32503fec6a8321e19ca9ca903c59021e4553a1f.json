{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nrequire(\"core-js/modules/es.array.filter.js\");\nrequire(\"core-js/modules/es.array.find.js\");\nrequire(\"core-js/modules/es.array.includes.js\");\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.array.splice.js\");\nrequire(\"core-js/modules/es.function.name.js\");\nrequire(\"core-js/modules/es.number.constructor.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/es.string.includes.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.filter.js\");\nrequire(\"core-js/modules/esnext.iterator.find.js\");\nrequire(\"core-js/modules/esnext.iterator.for-each.js\");\nrequire(\"core-js/modules/esnext.iterator.reduce.js\");\nrequire(\"core-js/modules/esnext.iterator.some.js\");\nrequire(\"core-js/modules/web.dom-collections.for-each.js\");\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _typeof2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/typeof.js\"));\nvar _litigation = require(\"@/api/litigation/litigation\");\nvar _daily_expense_approval = require(\"@/api/daily_expense_approval/daily_expense_approval\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default2 = exports.default = {\n  name: 'LitigationFeeForm',\n  props: {\n    data: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  watch: {\n    data: {\n      handler: function handler(newVal) {\n        if (newVal) {\n          console.log('接收到的数据:', newVal);\n          var caseId = newVal.序号;\n          console.log('案件ID:', caseId, '类型:', (0, _typeof2.default)(caseId));\n          this.litigationFee = {\n            litigationCaseId: caseId ? Number(caseId) : null,\n            贷款人: String(newVal.贷款人 || ''),\n            出单渠道: String(newVal.出单渠道 || ''),\n            放款银行: String(newVal.放款银行 || ''),\n            totalMoney: 0\n          };\n          this.litigationFees = [];\n          // 检查已提交的限制性费用类型\n          if (caseId) {\n            this.checkSubmittedLimitedTypes(caseId);\n          }\n        }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  data: function data() {\n    return {\n      dialogVisible: false,\n      litigationFee: {\n        litigationCaseId: '',\n        贷款人: '',\n        出单渠道: '',\n        放款银行: '',\n        totalMoney: 0\n      },\n      // 法诉费用提交数据\n      litigationFees: [],\n      // 记录费用的列表\n      litigationFeeTypeOptions: [{\n        label: '律师费',\n        value: 'lawyerFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '法诉费',\n        value: 'litigationFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '保全费',\n        value: 'preservationFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '布控费',\n        value: 'surveillanceFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '公告费',\n        value: 'announcementFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '评估费',\n        value: 'appraisalFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '执行费',\n        value: 'executionFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '违约金',\n        value: 'penalty',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '担保费',\n        value: 'guaranteeFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '居间费',\n        value: 'intermediaryFee',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '代偿金',\n        value: 'compensity',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '判决金额',\n        value: 'judgmentAmount',\n        category: 'judgment_interest',\n        multiple: false\n      }, {\n        label: '利息',\n        value: 'interest',\n        category: 'judgment_interest',\n        multiple: false\n      }, {\n        label: '其他欠款',\n        value: 'otherAmountsOwed',\n        category: 'litigation_fees',\n        multiple: true\n      }, {\n        label: '保险费',\n        value: 'insurance',\n        category: 'litigation_fees',\n        multiple: true\n      }],\n      // 已提交的限制性费用类型\n      submittedLimitedTypes: [],\n      // 日常费用申请相关\n      dailyExpenseDialogVisible: false,\n      dailyExpenseForm: {\n        litigationCaseId: '',\n        loanId: null,\n        status: 1,\n        // 法诉提交\n        expenseType: '',\n        expenseAmount: '',\n        expenseDate: '',\n        expenseDescription: '',\n        applicantId: '',\n        applicantName: '',\n        applicationTime: '',\n        approvalStatus: '0'\n      },\n      dailyExpenseRules: {\n        expenseType: [{\n          required: true,\n          message: '请选择费用类型',\n          trigger: 'change'\n        }],\n        expenseAmount: [{\n          required: true,\n          message: '请输入费用金额',\n          trigger: 'blur'\n        }, {\n          pattern: /^\\d+(\\.\\d{1,2})?$/,\n          message: '请输入正确的金额格式',\n          trigger: 'blur'\n        }],\n        expenseDate: [{\n          required: true,\n          message: '请选择费用发生日期',\n          trigger: 'change'\n        }]\n      },\n      // 日常费用列表弹窗\n      dailyExpenseListDialogVisible: false,\n      dailyExpenseList: [],\n      dailyExpenseListLoading: false\n    };\n  },\n  methods: {\n    // 新增法诉费用行\n    addLitigationFee: function addLitigationFee() {\n      this.litigationFees.push({\n        type: '',\n        amount: ''\n      });\n    },\n    // 判断某个类型是否已被其它行占用或者是限制性类型且已提交\n    isTypeDisabled: function isTypeDisabled(type, currentIndex) {\n      // 检查是否在当前表单中重复\n      var isDuplicateInForm = this.litigationFees.some(function (fee, idx) {\n        return fee.type === type && idx !== currentIndex;\n      });\n\n      // 检查是否是限制性类型且已提交过\n      var typeOption = this.litigationFeeTypeOptions.find(function (option) {\n        return option.value === type;\n      });\n      var isLimitedAndSubmitted = typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(type);\n      return isDuplicateInForm || isLimitedAndSubmitted;\n    },\n    // 选中后校验：若重复或限制性类型已提交则清空\n    handleLitigationFeeTypeChange: function handleLitigationFeeTypeChange(item, index) {\n      var duplicate = this.litigationFees.some(function (fee, idx) {\n        return fee.type === item.type && idx !== index;\n      });\n      if (duplicate) {\n        this.$message.warning('该费用类型已选择，请勿重复！');\n        this.$set(this.litigationFees[index], 'type', '');\n        return;\n      }\n\n      // 检查是否是限制性类型且已提交\n      var typeOption = this.litigationFeeTypeOptions.find(function (option) {\n        return option.value === item.type;\n      });\n      if (typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(item.type)) {\n        this.$message.warning(\"\".concat(typeOption.label, \"\\u53EA\\u80FD\\u63D0\\u4EA4\\u4E00\\u6B21\\uFF0C\\u5DF2\\u7ECF\\u63D0\\u4EA4\\u8FC7\\u4E86\\uFF01\"));\n        this.$set(this.litigationFees[index], 'type', '');\n        return;\n      }\n\n      // 如果是日常报销类型，提示用户应该在日常费用审批中提交\n      if (item.type === 'dailyReimbursement') {\n        this.$message.warning('日常报销费用请在\"日常费用审批\"模块中提交！');\n        this.$set(this.litigationFees[index], 'type', '');\n        return;\n      }\n    },\n    // 金额变化时重新计算总金额\n    handleLitigationFeeAmountChange: function handleLitigationFeeAmountChange() {\n      this.litigationFee.totalMoney = this.litigationFees.reduce(function (sum, fee) {\n        return sum + Number(fee.amount || 0);\n      }, 0);\n    },\n    // 删除法诉费用行\n    removeLitigationFee: function removeLitigationFee(index) {\n      this.litigationFees.splice(index, 1);\n      this.handleLitigationFeeAmountChange();\n    },\n    submitForm: function submitForm() {\n      var _this = this;\n      // 验证是否有费用项目\n      if (this.litigationFees.length === 0) {\n        this.$message.warning('请至少添加一项费用！');\n        return;\n      }\n\n      // 验证所有费用项目是否完整\n      var hasIncompleteItem = this.litigationFees.some(function (fee) {\n        return !fee.type || !fee.amount;\n      });\n      if (hasIncompleteItem) {\n        this.$message.warning('请完善所有费用项目的类型和金额！');\n        return;\n      }\n\n      // 分离日常费用和其他费用\n      var dailyExpenses = this.litigationFees.filter(function (fee) {\n        return fee.type === 'dailyReimbursement';\n      });\n      var otherFees = this.litigationFees.filter(function (fee) {\n        return fee.type !== 'dailyReimbursement';\n      });\n\n      // 如果有日常费用，提示用户\n      if (dailyExpenses.length > 0) {\n        this.$message.warning('检测到日常报销费用，请在\"日常费用审批\"模块中提交！');\n        return;\n      }\n\n      // 构建提交数据\n      var litigationFee = (0, _objectSpread2.default)({}, this.litigationFee);\n\n      // 确保 litigationCaseId 是数字类型\n      if (litigationFee.litigationCaseId) {\n        litigationFee.litigationCaseId = Number(litigationFee.litigationCaseId);\n      } else {\n        this.$message.error('缺少法诉案件ID，请重新选择案件');\n        return;\n      }\n\n      // 将费用列表中的数据动态填入到 litigationFee 对象中\n      otherFees.forEach(function (_ref) {\n        var type = _ref.type,\n          amount = _ref.amount;\n        if (type) litigationFee[type] = amount || 0;\n      });\n      console.log('提交的法诉费用数据:', litigationFee);\n\n      // 调用 API 提交数据\n      (0, _litigation.addLitigation_cost)(litigationFee).then(function (res) {\n        if (res.code === 200) {\n          _this.$message.success('提交成功');\n          _this.dialogVisible = false;\n          _this.reset();\n          // 刷新父组件数据\n          _this.$emit('refresh');\n        } else {\n          _this.$message.error('提交失败：' + (res.msg || '未知错误'));\n        }\n      }).catch(function (error) {\n        console.error('提交失败:', error);\n        _this.$message.error('提交失败，请稍后重试');\n      });\n    },\n    cancel: function cancel() {\n      this.dialogVisible = false;\n      this.reset();\n    },\n    open: function open() {\n      this.reset();\n      this.dialogVisible = true;\n    },\n    // 检查已提交的限制性费用类型\n    checkSubmittedLimitedTypes: function checkSubmittedLimitedTypes(litigationCaseId) {\n      var _this2 = this;\n      if (!litigationCaseId) return;\n\n      // 调用后端API检查已提交的判决金额和利息\n      (0, _litigation.checkSubmittedLimitedFees)(litigationCaseId).then(function (res) {\n        if (res.code === 200) {\n          _this2.submittedLimitedTypes = res.data || [];\n        }\n      }).catch(function (error) {\n        console.error('检查限制性费用类型失败:', error);\n        _this2.submittedLimitedTypes = [];\n      });\n    },\n    // 打开日常费用申请弹窗\n    openDailyExpenseDialog: function openDailyExpenseDialog() {\n      var _this$$store$state$us, _this$$store$state$us2;\n      var litigationCaseId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      this.resetDailyExpenseForm();\n      this.dailyExpenseForm.litigationCaseId = String(litigationCaseId || this.litigationFee.litigationCaseId || '');\n      this.dailyExpenseForm.status = 1; // 法诉提交\n      this.dailyExpenseForm.applicantId = String(((_this$$store$state$us = this.$store.state.user) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.id) || '');\n      this.dailyExpenseForm.applicantName = String(((_this$$store$state$us2 = this.$store.state.user) === null || _this$$store$state$us2 === void 0 ? void 0 : _this$$store$state$us2.name) || '');\n      this.dailyExpenseForm.applicationTime = new Date().toISOString().split('T')[0];\n      this.dailyExpenseDialogVisible = true;\n    },\n    // 取消日常费用申请\n    cancelDailyExpense: function cancelDailyExpense() {\n      this.dailyExpenseDialogVisible = false;\n      this.resetDailyExpenseForm();\n    },\n    // 提交日常费用申请\n    submitDailyExpense: function submitDailyExpense() {\n      var _this3 = this;\n      this.$refs.dailyExpenseForm.validate(function (valid) {\n        if (valid) {\n          // 法诉提交：确保有 litigationCaseId 和 status = 1\n          var submitData = (0, _objectSpread2.default)({}, _this3.dailyExpenseForm);\n          if (!submitData.litigationCaseId) {\n            _this3.$message.error('缺少法诉案件ID，请重新选择案件');\n            return;\n          }\n          submitData.status = 1; // 确保是法诉提交\n          (0, _daily_expense_approval.addDaily_expense_approval)(submitData).then(function (res) {\n            if (res.code === 200) {\n              _this3.$message.success('日常费用申请提交成功');\n              _this3.dailyExpenseDialogVisible = false;\n              _this3.resetDailyExpenseForm();\n            } else {\n              _this3.$message.error('提交失败：' + (res.msg || '未知错误'));\n            }\n          }).catch(function (error) {\n            console.error('提交失败:', error);\n            _this3.$message.error('提交失败，请稍后重试');\n          });\n        }\n      });\n    },\n    // 重置日常费用表单\n    resetDailyExpenseForm: function resetDailyExpenseForm() {\n      this.dailyExpenseForm = {\n        litigationCaseId: '',\n        loanId: null,\n        status: 1,\n        // 法诉提交\n        expenseType: '',\n        expenseAmount: '',\n        expenseDate: '',\n        expenseDescription: '',\n        applicantId: '',\n        applicantName: '',\n        applicationTime: '',\n        approvalStatus: '0'\n      };\n      if (this.$refs.dailyExpenseForm) {\n        this.$refs.dailyExpenseForm.resetFields();\n      }\n    },\n    reset: function reset() {\n      this.litigationFee = {\n        litigationCaseId: '',\n        贷款人: '',\n        出单渠道: '',\n        放款银行: '',\n        totalMoney: 0\n      };\n      this.litigationFees = [];\n      this.submittedLimitedTypes = [];\n    },\n    // 查看日常费用列表\n    viewDailyExpenseList: function viewDailyExpenseList() {\n      var _this4 = this;\n      this.dailyExpenseListLoading = true;\n      this.dailyExpenseListDialogVisible = true;\n      var queryParams = {\n        litigationCaseId: this.litigationFee.litigationCaseId,\n        pageNum: 1,\n        pageSize: 100\n      };\n      (0, _daily_expense_approval.listDaily_expense_approval)(queryParams).then(function (res) {\n        if (res.code === 200) {\n          _this4.dailyExpenseList = res.rows || [];\n        } else {\n          _this4.$message.error('获取日常费用列表失败：' + (res.msg || '未知错误'));\n          _this4.dailyExpenseList = [];\n        }\n        _this4.dailyExpenseListLoading = false;\n      }).catch(function (error) {\n        console.error('获取日常费用列表失败:', error);\n        _this4.$message.error('获取日常费用列表失败，请稍后重试');\n        _this4.dailyExpenseList = [];\n        _this4.dailyExpenseListLoading = false;\n      });\n    },\n    // 获取费用类型标签\n    getExpenseTypeLabel: function getExpenseTypeLabel(type) {\n      var typeMap = {\n        'oil_fee': '油费',\n        'road_fee': '路费',\n        'meal_fee': '餐费',\n        'accommodation_fee': '住宿费',\n        'transport_fee': '交通费',\n        'parking_fee': '停车费',\n        'communication_fee': '通讯费',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n    // 获取审批状态文本\n    getStatusText: function getStatusText(status) {\n      var statusMap = {\n        '0': '待审批',\n        '1': '全部通过',\n        '2': '已拒绝',\n        '3': '主管审批中',\n        '4': '总监审批中',\n        '5': '财务主管审批中',\n        '6': '总经理审批中'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    // 获取审批状态标签类型\n    getStatusTagType: function getStatusTagType(status) {\n      var typeMap = {\n        '0': 'warning',\n        // 待审批 - 橙色\n        '1': 'success',\n        // 全部通过 - 绿色\n        '2': 'danger',\n        // 已拒绝 - 红色\n        '3': 'primary',\n        // 主管审批中 - 蓝色\n        '4': 'primary',\n        // 总监审批中 - 蓝色\n        '5': 'primary',\n        // 财务主管审批中 - 蓝色\n        '6': 'primary' // 总经理审批中 - 蓝色\n      };\n      return typeMap[status] || 'info';\n    }\n  }\n};", "map": {"version": 3, "names": ["_litigation", "require", "_daily_expense_approval", "name", "props", "data", "type", "Object", "default", "watch", "handler", "newVal", "console", "log", "caseId", "序号", "_typeof2", "litigationFee", "litigationCaseId", "Number", "贷款人", "String", "出单渠道", "放款银行", "totalMoney", "litigationFees", "checkSubmittedLimitedTypes", "immediate", "deep", "dialogVisible", "litigationFeeTypeOptions", "label", "value", "category", "multiple", "submittedLimitedTypes", "dailyExpenseDialogVisible", "dailyExpenseForm", "loanId", "status", "expenseType", "expenseAmount", "expenseDate", "expenseDescription", "applicantId", "applicantName", "applicationTime", "approvalStatus", "dailyExpenseRules", "required", "message", "trigger", "pattern", "dailyExpenseListDialogVisible", "dailyExpenseList", "dailyExpenseListLoading", "methods", "addLitigationFee", "push", "amount", "isTypeDisabled", "currentIndex", "isDuplicateInForm", "some", "fee", "idx", "typeOption", "find", "option", "isLimitedAndSubmitted", "includes", "handleLitigationFeeTypeChange", "item", "index", "duplicate", "$message", "warning", "$set", "concat", "handleLitigationFeeAmountChange", "reduce", "sum", "removeLitigationFee", "splice", "submitForm", "_this", "length", "hasIncompleteItem", "dailyExpenses", "filter", "otherFees", "_objectSpread2", "error", "for<PERSON>ach", "_ref", "addLitigation_cost", "then", "res", "code", "success", "reset", "$emit", "msg", "catch", "cancel", "open", "_this2", "checkSubmittedLimitedFees", "openDailyExpenseDialog", "_this$$store$state$us", "_this$$store$state$us2", "arguments", "undefined", "resetDailyExpenseForm", "$store", "state", "user", "id", "Date", "toISOString", "split", "cancelDailyExpense", "submitDailyExpense", "_this3", "$refs", "validate", "valid", "submitData", "addDaily_expense_approval", "resetFields", "viewDailyExpenseList", "_this4", "queryParams", "pageNum", "pageSize", "listDaily_expense_approval", "rows", "getExpenseTypeLabel", "typeMap", "getStatusText", "statusMap", "getStatusTagType"], "sources": ["src/views/litigation/litigation/modules/litigationFeeForm.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 法诉费用提交弹窗 -->\r\n    <el-dialog title=\"提交法诉费用\" :visible.sync=\"dialogVisible\" width=\"800px\" append-to-body>\r\n    <el-descriptions :column=\"1\" border style=\"margin-bottom: 20px\">\r\n      <el-descriptions-item label=\"贷款人\">{{ litigationFee.贷款人 }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"出单渠道\">{{ litigationFee.出单渠道 }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"放款银行\">{{ litigationFee.放款银行 }}</el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <div style=\"padding: 0 20px\">\r\n      <!-- 费用提交规则提示 -->\r\n      <div class=\"fee-rules-container\">\r\n        <div class=\"fee-rules-header\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"fee-rules-title\">费用提交规则</span>\r\n        </div>\r\n        <div class=\"fee-rules-content\">\r\n          <div class=\"rule-item\">\r\n            <span class=\"rule-bullet\">•</span>\r\n            <span><strong>判决金额</strong>和<strong>利息</strong>每个案件只能提交一次</span>\r\n          </div>\r\n          <div class=\"rule-item\">\r\n            <span class=\"rule-bullet\">•</span>\r\n            <span><strong>日常费用</strong>（如油费、路费、餐费等）请在\"日常费用审批\"模块中提交</span>\r\n          </div>\r\n          <div class=\"rule-item\">\r\n            <span class=\"rule-bullet\">•</span>\r\n            <span>其他诉讼费用可以多次提交</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"margin-bottom: 20px\">\r\n        <div style=\"display: flex; align-items: flex-start\">\r\n          <label style=\"width: 120px; text-align: right; margin-right: 10px; font-weight: bold; line-height: 32px; flex-shrink: 0\">法诉费用：</label>\r\n          <div style=\"flex: 1\">\r\n            <div v-for=\"(item, index) in litigationFees\" :key=\"index\" style=\"margin-bottom: 10px\">\r\n              <el-row :gutter=\"10\" type=\"flex\" align=\"middle\">\r\n                <el-col :span=\"8\">\r\n                  <el-select\r\n                    v-model=\"item.type\"\r\n                    placeholder=\"请选择法诉费用类型\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleLitigationFeeTypeChange(item, index)\">\r\n                    <el-option\r\n                      v-for=\"option in litigationFeeTypeOptions\"\r\n                      :key=\"option.value\"\r\n                      :label=\"option.label\"\r\n                      :value=\"option.value\"\r\n                      :disabled=\"isTypeDisabled(option.value, index)\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-input\r\n                    v-model=\"item.amount\"\r\n                    placeholder=\"请输入金额\"\r\n                    @input=\"handleLitigationFeeAmountChange\"\r\n                    :disabled=\"item.type === ''\">\r\n                    <template slot=\"prepend\">￥</template>\r\n                  </el-input>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-button type=\"danger\" icon=\"el-icon-delete\" style=\"width: 54px\" @click=\"removeLitigationFee(index)\" />\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 新增按钮 -->\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addLitigationFee\">新增法诉费用</el-button>\r\n            <el-button type=\"success\" size=\"small\" icon=\"el-icon-document\" @click=\"openDailyExpenseDialog\" style=\"margin-left: 10px\">申请日常费用</el-button>\r\n            <el-button type=\"info\" size=\"small\" icon=\"el-icon-view\" @click=\"viewDailyExpenseList\" style=\"margin-left: 10px\">查看日常费用</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"margin-bottom: 20px\">\r\n        <label style=\"display: inline-block; width: 120px; text-align: right; margin-right: 10px; font-weight: bold\">合计法诉费用：</label>\r\n        <div style=\"display: inline-block; width: calc(100% - 130px)\">\r\n          <el-input v-model=\"litigationFee.totalMoney\" placeholder=\"请输入合计法诉费用\" disabled>\r\n            <template slot=\"prepend\">￥</template>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n\r\n  <!-- 日常费用申请弹窗 -->\r\n  <el-dialog title=\"申请日常费用\" :visible.sync=\"dailyExpenseDialogVisible\" width=\"600px\" append-to-body>\r\n    <el-form ref=\"dailyExpenseForm\" :model=\"dailyExpenseForm\" :rules=\"dailyExpenseRules\" label-width=\"120px\">\r\n      <el-form-item label=\"费用类型\" prop=\"expenseType\">\r\n        <el-select v-model=\"dailyExpenseForm.expenseType\" placeholder=\"请选择费用类型\" style=\"width: 100%\">\r\n          <el-option label=\"油费\" value=\"oil_fee\" />\r\n          <el-option label=\"路费\" value=\"road_fee\" />\r\n          <el-option label=\"餐费\" value=\"meal_fee\" />\r\n          <el-option label=\"住宿费\" value=\"accommodation_fee\" />\r\n          <el-option label=\"交通费\" value=\"transport_fee\" />\r\n          <el-option label=\"停车费\" value=\"parking_fee\" />\r\n          <el-option label=\"通讯费\" value=\"communication_fee\" />\r\n          <el-option label=\"其他\" value=\"other\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"费用金额\" prop=\"expenseAmount\">\r\n        <el-input v-model=\"dailyExpenseForm.expenseAmount\" placeholder=\"请输入费用金额\">\r\n          <template slot=\"prepend\">￥</template>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"费用发生日期\" prop=\"expenseDate\">\r\n        <el-date-picker\r\n          v-model=\"dailyExpenseForm.expenseDate\"\r\n          type=\"date\"\r\n          placeholder=\"请选择费用发生日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          style=\"width: 100%\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"费用说明\" prop=\"expenseDescription\">\r\n        <el-input v-model=\"dailyExpenseForm.expenseDescription\" type=\"textarea\" placeholder=\"请输入费用说明\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"cancelDailyExpense\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submitDailyExpense\">确 定</el-button>\r\n    </div>\r\n  </el-dialog>\r\n\r\n  <!-- 查看日常费用列表弹窗 -->\r\n  <el-dialog title=\"日常费用申请记录\" :visible.sync=\"dailyExpenseListDialogVisible\" width=\"800px\" append-to-body>\r\n    <el-table :data=\"dailyExpenseList\" v-loading=\"dailyExpenseListLoading\">\r\n      <el-table-column label=\"费用类型\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getExpenseTypeLabel(scope.row.expenseType) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"费用金额\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          ￥{{ scope.row.expenseAmount }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"费用发生日期\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.expenseDate }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请时间\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.applicationTime }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批状态\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\r\n            {{ getStatusText(scope.row.approvalStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"费用说明\" align=\"center\" prop=\"expenseDescription\" />\r\n    </el-table>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"dailyExpenseListDialogVisible = false\">关 闭</el-button>\r\n    </div>\r\n  </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.fee-rules-container {\r\n  background-color: #f4f4f5;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.fee-rules-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.fee-rules-header .el-icon-info {\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.fee-rules-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.fee-rules-content {\r\n  padding-left: 24px;\r\n}\r\n\r\n.rule-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.rule-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.rule-bullet {\r\n  color: #409EFF;\r\n  margin-right: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.rule-item span {\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.rule-item strong {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { addLitigation_cost, checkSubmittedLimitedFees } from '@/api/litigation/litigation'\r\nimport { addDaily_expense_approval, listDaily_expense_approval } from '@/api/daily_expense_approval/daily_expense_approval'\r\n\r\nexport default {\r\n  name: 'LitigationFeeForm',\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          console.log('接收到的数据:', newVal)\r\n          const caseId = newVal.序号\r\n          console.log('案件ID:', caseId, '类型:', typeof caseId)\r\n\r\n          this.litigationFee = {\r\n            litigationCaseId: caseId ? Number(caseId) : null,\r\n            贷款人: String(newVal.贷款人 || ''),\r\n            出单渠道: String(newVal.出单渠道 || ''),\r\n            放款银行: String(newVal.放款银行 || ''),\r\n            totalMoney: 0,\r\n          }\r\n          this.litigationFees = []\r\n          // 检查已提交的限制性费用类型\r\n          if (caseId) {\r\n            this.checkSubmittedLimitedTypes(caseId)\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      litigationFee: {\r\n        litigationCaseId: '',\r\n        贷款人: '',\r\n        出单渠道: '',\r\n        放款银行: '',\r\n        totalMoney: 0,\r\n      }, // 法诉费用提交数据\r\n      litigationFees: [], // 记录费用的列表\r\n      litigationFeeTypeOptions: [\r\n        { label: '律师费', value: 'lawyerFee', category: 'litigation_fees', multiple: true },\r\n        { label: '法诉费', value: 'litigationFee', category: 'litigation_fees', multiple: true },\r\n        { label: '保全费', value: 'preservationFee', category: 'litigation_fees', multiple: true },\r\n        { label: '布控费', value: 'surveillanceFee', category: 'litigation_fees', multiple: true },\r\n        { label: '公告费', value: 'announcementFee', category: 'litigation_fees', multiple: true },\r\n        { label: '评估费', value: 'appraisalFee', category: 'litigation_fees', multiple: true },\r\n        { label: '执行费', value: 'executionFee', category: 'litigation_fees', multiple: true },\r\n        { label: '违约金', value: 'penalty', category: 'litigation_fees', multiple: true },\r\n        { label: '担保费', value: 'guaranteeFee', category: 'litigation_fees', multiple: true },\r\n        { label: '居间费', value: 'intermediaryFee', category: 'litigation_fees', multiple: true },\r\n        { label: '代偿金', value: 'compensity', category: 'litigation_fees', multiple: true },\r\n        { label: '判决金额', value: 'judgmentAmount', category: 'judgment_interest', multiple: false },\r\n        { label: '利息', value: 'interest', category: 'judgment_interest', multiple: false },\r\n        { label: '其他欠款', value: 'otherAmountsOwed', category: 'litigation_fees', multiple: true },\r\n        { label: '保险费', value: 'insurance', category: 'litigation_fees', multiple: true },\r\n      ],\r\n      // 已提交的限制性费用类型\r\n      submittedLimitedTypes: [],\r\n\r\n      // 日常费用申请相关\r\n      dailyExpenseDialogVisible: false,\r\n      dailyExpenseForm: {\r\n        litigationCaseId: '',\r\n        loanId: null,\r\n        status: 1, // 法诉提交\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      },\r\n      dailyExpenseRules: {\r\n        expenseType: [\r\n          { required: true, message: '请选择费用类型', trigger: 'change' }\r\n        ],\r\n        expenseAmount: [\r\n          { required: true, message: '请输入费用金额', trigger: 'blur' },\r\n          { pattern: /^\\d+(\\.\\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }\r\n        ],\r\n        expenseDate: [\r\n          { required: true, message: '请选择费用发生日期', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 日常费用列表弹窗\r\n      dailyExpenseListDialogVisible: false,\r\n      dailyExpenseList: [],\r\n      dailyExpenseListLoading: false\r\n    }\r\n  },\r\n  methods: {\r\n    // 新增法诉费用行\r\n    addLitigationFee() {\r\n      this.litigationFees.push({\r\n        type: '',\r\n        amount: '',\r\n      })\r\n    },\r\n\r\n    // 判断某个类型是否已被其它行占用或者是限制性类型且已提交\r\n    isTypeDisabled(type, currentIndex) {\r\n      // 检查是否在当前表单中重复\r\n      const isDuplicateInForm = this.litigationFees.some((fee, idx) => fee.type === type && idx !== currentIndex)\r\n\r\n      // 检查是否是限制性类型且已提交过\r\n      const typeOption = this.litigationFeeTypeOptions.find(option => option.value === type)\r\n      const isLimitedAndSubmitted = typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(type)\r\n\r\n      return isDuplicateInForm || isLimitedAndSubmitted\r\n    },\r\n\r\n    // 选中后校验：若重复或限制性类型已提交则清空\r\n    handleLitigationFeeTypeChange(item, index) {\r\n      const duplicate = this.litigationFees.some((fee, idx) => fee.type === item.type && idx !== index)\r\n      if (duplicate) {\r\n        this.$message.warning('该费用类型已选择，请勿重复！')\r\n        this.$set(this.litigationFees[index], 'type', '')\r\n        return\r\n      }\r\n\r\n      // 检查是否是限制性类型且已提交\r\n      const typeOption = this.litigationFeeTypeOptions.find(option => option.value === item.type)\r\n      if (typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(item.type)) {\r\n        this.$message.warning(`${typeOption.label}只能提交一次，已经提交过了！`)\r\n        this.$set(this.litigationFees[index], 'type', '')\r\n        return\r\n      }\r\n\r\n      // 如果是日常报销类型，提示用户应该在日常费用审批中提交\r\n      if (item.type === 'dailyReimbursement') {\r\n        this.$message.warning('日常报销费用请在\"日常费用审批\"模块中提交！')\r\n        this.$set(this.litigationFees[index], 'type', '')\r\n        return\r\n      }\r\n    },\r\n\r\n    // 金额变化时重新计算总金额\r\n    handleLitigationFeeAmountChange() {\r\n      this.litigationFee.totalMoney = this.litigationFees.reduce((sum, fee) => sum + Number(fee.amount || 0), 0)\r\n    },\r\n\r\n    // 删除法诉费用行\r\n    removeLitigationFee(index) {\r\n      this.litigationFees.splice(index, 1)\r\n      this.handleLitigationFeeAmountChange()\r\n    },\r\n\r\n    submitForm() {\r\n      // 验证是否有费用项目\r\n      if (this.litigationFees.length === 0) {\r\n        this.$message.warning('请至少添加一项费用！')\r\n        return\r\n      }\r\n\r\n      // 验证所有费用项目是否完整\r\n      const hasIncompleteItem = this.litigationFees.some(fee => !fee.type || !fee.amount)\r\n      if (hasIncompleteItem) {\r\n        this.$message.warning('请完善所有费用项目的类型和金额！')\r\n        return\r\n      }\r\n\r\n      // 分离日常费用和其他费用\r\n      const dailyExpenses = this.litigationFees.filter(fee => fee.type === 'dailyReimbursement')\r\n      const otherFees = this.litigationFees.filter(fee => fee.type !== 'dailyReimbursement')\r\n\r\n      // 如果有日常费用，提示用户\r\n      if (dailyExpenses.length > 0) {\r\n        this.$message.warning('检测到日常报销费用，请在\"日常费用审批\"模块中提交！')\r\n        return\r\n      }\r\n\r\n      // 构建提交数据\r\n      const litigationFee = { ...this.litigationFee }\r\n\r\n      // 确保 litigationCaseId 是数字类型\r\n      if (litigationFee.litigationCaseId) {\r\n        litigationFee.litigationCaseId = Number(litigationFee.litigationCaseId)\r\n      } else {\r\n        this.$message.error('缺少法诉案件ID，请重新选择案件')\r\n        return\r\n      }\r\n\r\n      // 将费用列表中的数据动态填入到 litigationFee 对象中\r\n      otherFees.forEach(({ type, amount }) => {\r\n        if (type) litigationFee[type] = amount || 0\r\n      })\r\n\r\n      console.log('提交的法诉费用数据:', litigationFee)\r\n\r\n      // 调用 API 提交数据\r\n      addLitigation_cost(litigationFee).then(res => {\r\n        if (res.code === 200) {\r\n          this.$message.success('提交成功')\r\n          this.dialogVisible = false\r\n          this.reset()\r\n          // 刷新父组件数据\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message.error('提交失败：' + (res.msg || '未知错误'))\r\n        }\r\n      }).catch(error => {\r\n        console.error('提交失败:', error)\r\n        this.$message.error('提交失败，请稍后重试')\r\n      })\r\n    },\r\n\r\n    cancel() {\r\n      this.dialogVisible = false\r\n      this.reset()\r\n    },\r\n\r\n    open() {\r\n      this.reset()\r\n      this.dialogVisible = true\r\n    },\r\n\r\n    // 检查已提交的限制性费用类型\r\n    checkSubmittedLimitedTypes(litigationCaseId) {\r\n      if (!litigationCaseId) return\r\n\r\n      // 调用后端API检查已提交的判决金额和利息\r\n      checkSubmittedLimitedFees(litigationCaseId).then(res => {\r\n        if (res.code === 200) {\r\n          this.submittedLimitedTypes = res.data || []\r\n        }\r\n      }).catch(error => {\r\n        console.error('检查限制性费用类型失败:', error)\r\n        this.submittedLimitedTypes = []\r\n      })\r\n    },\r\n\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog(litigationCaseId = null) {\r\n      this.resetDailyExpenseForm()\r\n      this.dailyExpenseForm.litigationCaseId = String(litigationCaseId || this.litigationFee.litigationCaseId || '')\r\n      this.dailyExpenseForm.status = 1 // 法诉提交\r\n      this.dailyExpenseForm.applicantId = String(this.$store.state.user?.id || '')\r\n      this.dailyExpenseForm.applicantName = String(this.$store.state.user?.name || '')\r\n      this.dailyExpenseForm.applicationTime = new Date().toISOString().split('T')[0]\r\n      this.dailyExpenseDialogVisible = true\r\n    },\r\n\r\n    // 取消日常费用申请\r\n    cancelDailyExpense() {\r\n      this.dailyExpenseDialogVisible = false\r\n      this.resetDailyExpenseForm()\r\n    },\r\n\r\n    // 提交日常费用申请\r\n    submitDailyExpense() {\r\n      this.$refs.dailyExpenseForm.validate((valid) => {\r\n        if (valid) {\r\n          // 法诉提交：确保有 litigationCaseId 和 status = 1\r\n          const submitData = { ...this.dailyExpenseForm }\r\n          if (!submitData.litigationCaseId) {\r\n            this.$message.error('缺少法诉案件ID，请重新选择案件')\r\n            return\r\n          }\r\n          submitData.status = 1 // 确保是法诉提交\r\n          addDaily_expense_approval(submitData).then(res => {\r\n            if (res.code === 200) {\r\n              this.$message.success('日常费用申请提交成功')\r\n              this.dailyExpenseDialogVisible = false\r\n              this.resetDailyExpenseForm()\r\n            } else {\r\n              this.$message.error('提交失败：' + (res.msg || '未知错误'))\r\n            }\r\n          }).catch(error => {\r\n            console.error('提交失败:', error)\r\n            this.$message.error('提交失败，请稍后重试')\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 重置日常费用表单\r\n    resetDailyExpenseForm() {\r\n      this.dailyExpenseForm = {\r\n        litigationCaseId: '',\r\n        loanId: null,\r\n        status: 1, // 法诉提交\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      }\r\n      if (this.$refs.dailyExpenseForm) {\r\n        this.$refs.dailyExpenseForm.resetFields()\r\n      }\r\n    },\r\n\r\n    reset() {\r\n      this.litigationFee = {\r\n        litigationCaseId: '',\r\n        贷款人: '',\r\n        出单渠道: '',\r\n        放款银行: '',\r\n        totalMoney: 0,\r\n      }\r\n      this.litigationFees = []\r\n      this.submittedLimitedTypes = []\r\n    },\r\n\r\n    // 查看日常费用列表\r\n    viewDailyExpenseList() {\r\n      this.dailyExpenseListLoading = true\r\n      this.dailyExpenseListDialogVisible = true\r\n\r\n      const queryParams = {\r\n        litigationCaseId: this.litigationFee.litigationCaseId,\r\n        pageNum: 1,\r\n        pageSize: 100\r\n      }\r\n\r\n      listDaily_expense_approval(queryParams).then(res => {\r\n        if (res.code === 200) {\r\n          this.dailyExpenseList = res.rows || []\r\n        } else {\r\n          this.$message.error('获取日常费用列表失败：' + (res.msg || '未知错误'))\r\n          this.dailyExpenseList = []\r\n        }\r\n        this.dailyExpenseListLoading = false\r\n      }).catch(error => {\r\n        console.error('获取日常费用列表失败:', error)\r\n        this.$message.error('获取日常费用列表失败，请稍后重试')\r\n        this.dailyExpenseList = []\r\n        this.dailyExpenseListLoading = false\r\n      })\r\n    },\r\n\r\n    // 获取费用类型标签\r\n    getExpenseTypeLabel(type) {\r\n      const typeMap = {\r\n        'oil_fee': '油费',\r\n        'road_fee': '路费',\r\n        'meal_fee': '餐费',\r\n        'accommodation_fee': '住宿费',\r\n        'transport_fee': '交通费',\r\n        'parking_fee': '停车费',\r\n        'communication_fee': '通讯费',\r\n        'other': '其他'\r\n      }\r\n      return typeMap[type] || type\r\n    },\r\n\r\n    // 获取审批状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '待审批',\r\n        '1': '全部通过',\r\n        '2': '已拒绝',\r\n        '3': '主管审批中',\r\n        '4': '总监审批中',\r\n        '5': '财务主管审批中',\r\n        '6': '总经理审批中'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    // 获取审批状态标签类型\r\n    getStatusTagType(status) {\r\n      const typeMap = {\r\n        '0': 'warning',    // 待审批 - 橙色\r\n        '1': 'success',    // 全部通过 - 绿色\r\n        '2': 'danger',     // 已拒绝 - 红色\r\n        '3': 'primary',    // 主管审批中 - 蓝色\r\n        '4': 'primary',    // 总监审批中 - 蓝色\r\n        '5': 'primary',    // 财务主管审批中 - 蓝色\r\n        '6': 'primary'     // 总经理审批中 - 蓝色\r\n      }\r\n      return typeMap[status] || 'info'\r\n    }\r\n  },\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAqOA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAE,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EACAC,KAAA;IACAJ,IAAA;MACAK,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACAC,OAAA,CAAAC,GAAA,YAAAF,MAAA;UACA,IAAAG,MAAA,GAAAH,MAAA,CAAAI,EAAA;UACAH,OAAA,CAAAC,GAAA,UAAAC,MAAA,aAAAE,QAAA,CAAAR,OAAA,EAAAM,MAAA;UAEA,KAAAG,aAAA;YACAC,gBAAA,EAAAJ,MAAA,GAAAK,MAAA,CAAAL,MAAA;YACAM,GAAA,EAAAC,MAAA,CAAAV,MAAA,CAAAS,GAAA;YACAE,IAAA,EAAAD,MAAA,CAAAV,MAAA,CAAAW,IAAA;YACAC,IAAA,EAAAF,MAAA,CAAAV,MAAA,CAAAY,IAAA;YACAC,UAAA;UACA;UACA,KAAAC,cAAA;UACA;UACA,IAAAX,MAAA;YACA,KAAAY,0BAAA,CAAAZ,MAAA;UACA;QACA;MACA;MACAa,SAAA;MACAC,IAAA;IACA;EACA;EACAvB,IAAA,WAAAA,KAAA;IACA;MACAwB,aAAA;MACAZ,aAAA;QACAC,gBAAA;QACAE,GAAA;QACAE,IAAA;QACAC,IAAA;QACAC,UAAA;MACA;MAAA;MACAC,cAAA;MAAA;MACAK,wBAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,GACA;QAAAH,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA,EACA;MACA;MACAC,qBAAA;MAEA;MACAC,yBAAA;MACAC,gBAAA;QACAnB,gBAAA;QACAoB,MAAA;QACAC,MAAA;QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,cAAA;MACA;MACAC,iBAAA;QACAR,WAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,aAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA,EACA;QACAT,WAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAE,6BAAA;MACAC,gBAAA;MACAC,uBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAhC,cAAA,CAAAiC,IAAA;QACApD,IAAA;QACAqD,MAAA;MACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAtD,IAAA,EAAAuD,YAAA;MACA;MACA,IAAAC,iBAAA,QAAArC,cAAA,CAAAsC,IAAA,WAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAD,GAAA,CAAA1D,IAAA,KAAAA,IAAA,IAAA2D,GAAA,KAAAJ,YAAA;MAAA;;MAEA;MACA,IAAAK,UAAA,QAAApC,wBAAA,CAAAqC,IAAA,WAAAC,MAAA;QAAA,OAAAA,MAAA,CAAApC,KAAA,KAAA1B,IAAA;MAAA;MACA,IAAA+D,qBAAA,GAAAH,UAAA,KAAAA,UAAA,CAAAhC,QAAA,SAAAC,qBAAA,CAAAmC,QAAA,CAAAhE,IAAA;MAEA,OAAAwD,iBAAA,IAAAO,qBAAA;IACA;IAEA;IACAE,6BAAA,WAAAA,8BAAAC,IAAA,EAAAC,KAAA;MACA,IAAAC,SAAA,QAAAjD,cAAA,CAAAsC,IAAA,WAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAD,GAAA,CAAA1D,IAAA,KAAAkE,IAAA,CAAAlE,IAAA,IAAA2D,GAAA,KAAAQ,KAAA;MAAA;MACA,IAAAC,SAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA,KAAAC,IAAA,MAAApD,cAAA,CAAAgD,KAAA;QACA;MACA;;MAEA;MACA,IAAAP,UAAA,QAAApC,wBAAA,CAAAqC,IAAA,WAAAC,MAAA;QAAA,OAAAA,MAAA,CAAApC,KAAA,KAAAwC,IAAA,CAAAlE,IAAA;MAAA;MACA,IAAA4D,UAAA,KAAAA,UAAA,CAAAhC,QAAA,SAAAC,qBAAA,CAAAmC,QAAA,CAAAE,IAAA,CAAAlE,IAAA;QACA,KAAAqE,QAAA,CAAAC,OAAA,IAAAE,MAAA,CAAAZ,UAAA,CAAAnC,KAAA;QACA,KAAA8C,IAAA,MAAApD,cAAA,CAAAgD,KAAA;QACA;MACA;;MAEA;MACA,IAAAD,IAAA,CAAAlE,IAAA;QACA,KAAAqE,QAAA,CAAAC,OAAA;QACA,KAAAC,IAAA,MAAApD,cAAA,CAAAgD,KAAA;QACA;MACA;IACA;IAEA;IACAM,+BAAA,WAAAA,gCAAA;MACA,KAAA9D,aAAA,CAAAO,UAAA,QAAAC,cAAA,CAAAuD,MAAA,WAAAC,GAAA,EAAAjB,GAAA;QAAA,OAAAiB,GAAA,GAAA9D,MAAA,CAAA6C,GAAA,CAAAL,MAAA;MAAA;IACA;IAEA;IACAuB,mBAAA,WAAAA,oBAAAT,KAAA;MACA,KAAAhD,cAAA,CAAA0D,MAAA,CAAAV,KAAA;MACA,KAAAM,+BAAA;IACA;IAEAK,UAAA,WAAAA,WAAA;MAAA,IAAAC,KAAA;MACA;MACA,SAAA5D,cAAA,CAAA6D,MAAA;QACA,KAAAX,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAW,iBAAA,QAAA9D,cAAA,CAAAsC,IAAA,WAAAC,GAAA;QAAA,QAAAA,GAAA,CAAA1D,IAAA,KAAA0D,GAAA,CAAAL,MAAA;MAAA;MACA,IAAA4B,iBAAA;QACA,KAAAZ,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAAY,aAAA,QAAA/D,cAAA,CAAAgE,MAAA,WAAAzB,GAAA;QAAA,OAAAA,GAAA,CAAA1D,IAAA;MAAA;MACA,IAAAoF,SAAA,QAAAjE,cAAA,CAAAgE,MAAA,WAAAzB,GAAA;QAAA,OAAAA,GAAA,CAAA1D,IAAA;MAAA;;MAEA;MACA,IAAAkF,aAAA,CAAAF,MAAA;QACA,KAAAX,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAA3D,aAAA,OAAA0E,cAAA,CAAAnF,OAAA,WAAAS,aAAA;;MAEA;MACA,IAAAA,aAAA,CAAAC,gBAAA;QACAD,aAAA,CAAAC,gBAAA,GAAAC,MAAA,CAAAF,aAAA,CAAAC,gBAAA;MACA;QACA,KAAAyD,QAAA,CAAAiB,KAAA;QACA;MACA;;MAEA;MACAF,SAAA,CAAAG,OAAA,WAAAC,IAAA;QAAA,IAAAxF,IAAA,GAAAwF,IAAA,CAAAxF,IAAA;UAAAqD,MAAA,GAAAmC,IAAA,CAAAnC,MAAA;QACA,IAAArD,IAAA,EAAAW,aAAA,CAAAX,IAAA,IAAAqD,MAAA;MACA;MAEA/C,OAAA,CAAAC,GAAA,eAAAI,aAAA;;MAEA;MACA,IAAA8E,8BAAA,EAAA9E,aAAA,EAAA+E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAb,KAAA,CAAAV,QAAA,CAAAwB,OAAA;UACAd,KAAA,CAAAxD,aAAA;UACAwD,KAAA,CAAAe,KAAA;UACA;UACAf,KAAA,CAAAgB,KAAA;QACA;UACAhB,KAAA,CAAAV,QAAA,CAAAiB,KAAA,YAAAK,GAAA,CAAAK,GAAA;QACA;MACA,GAAAC,KAAA,WAAAX,KAAA;QACAhF,OAAA,CAAAgF,KAAA,UAAAA,KAAA;QACAP,KAAA,CAAAV,QAAA,CAAAiB,KAAA;MACA;IACA;IAEAY,MAAA,WAAAA,OAAA;MACA,KAAA3E,aAAA;MACA,KAAAuE,KAAA;IACA;IAEAK,IAAA,WAAAA,KAAA;MACA,KAAAL,KAAA;MACA,KAAAvE,aAAA;IACA;IAEA;IACAH,0BAAA,WAAAA,2BAAAR,gBAAA;MAAA,IAAAwF,MAAA;MACA,KAAAxF,gBAAA;;MAEA;MACA,IAAAyF,qCAAA,EAAAzF,gBAAA,EAAA8E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAQ,MAAA,CAAAvE,qBAAA,GAAA8D,GAAA,CAAA5F,IAAA;QACA;MACA,GAAAkG,KAAA,WAAAX,KAAA;QACAhF,OAAA,CAAAgF,KAAA,iBAAAA,KAAA;QACAc,MAAA,CAAAvE,qBAAA;MACA;IACA;IAEA;IACAyE,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MAAA,IAAA5F,gBAAA,GAAA6F,SAAA,CAAAzB,MAAA,QAAAyB,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,KAAAE,qBAAA;MACA,KAAA5E,gBAAA,CAAAnB,gBAAA,GAAAG,MAAA,CAAAH,gBAAA,SAAAD,aAAA,CAAAC,gBAAA;MACA,KAAAmB,gBAAA,CAAAE,MAAA;MACA,KAAAF,gBAAA,CAAAO,WAAA,GAAAvB,MAAA,GAAAwF,qBAAA,QAAAK,MAAA,CAAAC,KAAA,CAAAC,IAAA,cAAAP,qBAAA,uBAAAA,qBAAA,CAAAQ,EAAA;MACA,KAAAhF,gBAAA,CAAAQ,aAAA,GAAAxB,MAAA,GAAAyF,sBAAA,QAAAI,MAAA,CAAAC,KAAA,CAAAC,IAAA,cAAAN,sBAAA,uBAAAA,sBAAA,CAAA3G,IAAA;MACA,KAAAkC,gBAAA,CAAAS,eAAA,OAAAwE,IAAA,GAAAC,WAAA,GAAAC,KAAA;MACA,KAAApF,yBAAA;IACA;IAEA;IACAqF,kBAAA,WAAAA,mBAAA;MACA,KAAArF,yBAAA;MACA,KAAA6E,qBAAA;IACA;IAEA;IACAS,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAvF,gBAAA,CAAAwF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAC,UAAA,OAAApC,cAAA,CAAAnF,OAAA,MAAAmH,MAAA,CAAAtF,gBAAA;UACA,KAAA0F,UAAA,CAAA7G,gBAAA;YACAyG,MAAA,CAAAhD,QAAA,CAAAiB,KAAA;YACA;UACA;UACAmC,UAAA,CAAAxF,MAAA;UACA,IAAAyF,iDAAA,EAAAD,UAAA,EAAA/B,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAyB,MAAA,CAAAhD,QAAA,CAAAwB,OAAA;cACAwB,MAAA,CAAAvF,yBAAA;cACAuF,MAAA,CAAAV,qBAAA;YACA;cACAU,MAAA,CAAAhD,QAAA,CAAAiB,KAAA,YAAAK,GAAA,CAAAK,GAAA;YACA;UACA,GAAAC,KAAA,WAAAX,KAAA;YACAhF,OAAA,CAAAgF,KAAA,UAAAA,KAAA;YACA+B,MAAA,CAAAhD,QAAA,CAAAiB,KAAA;UACA;QACA;MACA;IACA;IAIA;IACAqB,qBAAA,WAAAA,sBAAA;MACA,KAAA5E,gBAAA;QACAnB,gBAAA;QACAoB,MAAA;QACAC,MAAA;QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,cAAA;MACA;MACA,SAAA6E,KAAA,CAAAvF,gBAAA;QACA,KAAAuF,KAAA,CAAAvF,gBAAA,CAAA4F,WAAA;MACA;IACA;IAEA7B,KAAA,WAAAA,MAAA;MACA,KAAAnF,aAAA;QACAC,gBAAA;QACAE,GAAA;QACAE,IAAA;QACAC,IAAA;QACAC,UAAA;MACA;MACA,KAAAC,cAAA;MACA,KAAAU,qBAAA;IACA;IAEA;IACA+F,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,KAAA5E,uBAAA;MACA,KAAAF,6BAAA;MAEA,IAAA+E,WAAA;QACAlH,gBAAA,OAAAD,aAAA,CAAAC,gBAAA;QACAmH,OAAA;QACAC,QAAA;MACA;MAEA,IAAAC,kDAAA,EAAAH,WAAA,EAAApC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAiC,MAAA,CAAA7E,gBAAA,GAAA2C,GAAA,CAAAuC,IAAA;QACA;UACAL,MAAA,CAAAxD,QAAA,CAAAiB,KAAA,kBAAAK,GAAA,CAAAK,GAAA;UACA6B,MAAA,CAAA7E,gBAAA;QACA;QACA6E,MAAA,CAAA5E,uBAAA;MACA,GAAAgD,KAAA,WAAAX,KAAA;QACAhF,OAAA,CAAAgF,KAAA,gBAAAA,KAAA;QACAuC,MAAA,CAAAxD,QAAA,CAAAiB,KAAA;QACAuC,MAAA,CAAA7E,gBAAA;QACA6E,MAAA,CAAA5E,uBAAA;MACA;IACA;IAEA;IACAkF,mBAAA,WAAAA,oBAAAnI,IAAA;MACA,IAAAoI,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAApI,IAAA,KAAAA,IAAA;IACA;IAEA;IACAqI,aAAA,WAAAA,cAAApG,MAAA;MACA,IAAAqG,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAArG,MAAA;IACA;IAEA;IACAsG,gBAAA,WAAAA,iBAAAtG,MAAA;MACA,IAAAmG,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAnG,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}