{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.approveLitigationCostFlow = approveLitigationCostFlow;\nexports.approveLitigationCostRecord = approveLitigationCostRecord;\nexports.batchApproveLitigationCostFlow = batchApproveLitigationCostFlow;\nexports.batchApproveLitigationCostRecords = batchApproveLitigationCostRecords;\nexports.getApprovalStatistics = getApprovalStatistics;\nexports.getLitigationCostApproval = getLitigationCostApproval;\nexports.getLitigationCostSubmissionRecords = getLitigationCostSubmissionRecords;\nexports.getLitigationCostTypes = getLitigationCostTypes;\nexports.listLitigationCostApproval = listLitigationCostApproval;\nexports.rejectLitigationCostFlow = rejectLitigationCostFlow;\nexports.startLitigationApprovalFlow = startLitigationApprovalFlow;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）\nfunction listLitigationCostApproval(query) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询法诉费用审批详细\nfunction getLitigationCostApproval(id) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/' + id,\n    method: 'get'\n  });\n}\n\n// 获取法诉案件的费用提交记录详情（用于审批弹窗）\nfunction getLitigationCostSubmissionRecords(litigationCaseId) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/records/' + litigationCaseId,\n    method: 'get'\n  });\n}\n\n// 单个费用记录审批\nfunction approveLitigationCostRecord(data) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/approve',\n    method: 'put',\n    data: data\n  });\n}\n\n// 批量审批费用记录\nfunction batchApproveLitigationCostRecords(data) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/batchApprove',\n    method: 'put',\n    data: data\n  });\n}\n\n// 获取法诉费用类型字典\nfunction getLitigationCostTypes() {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/costTypes',\n    method: 'get'\n  });\n}\n\n// 获取审批状态统计\nfunction getApprovalStatistics() {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/statistics',\n    method: 'get'\n  });\n}\n\n// 开始审批流程\nfunction startLitigationApprovalFlow(id) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/startApproval/' + id,\n    method: 'post'\n  });\n}\n\n// 审批通过（进入下一个审批节点）\nfunction approveLitigationCostFlow(data) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/approveFlow',\n    method: 'put',\n    data: data\n  });\n}\n\n// 审批拒绝\nfunction rejectLitigationCostFlow(data) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/rejectFlow',\n    method: 'put',\n    data: data\n  });\n}\n\n// 批量审批流程\nfunction batchApproveLitigationCostFlow(data) {\n  return (0, _request.default)({\n    url: '/litigation_cost_approval/litigation_cost_approval/batchApproveFlow',\n    method: 'put',\n    data: data\n  });\n}", "map": {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listLitigationCostApproval", "query", "request", "url", "method", "params", "getLitigationCostApproval", "id", "getLitigationCostSubmissionRecords", "litigationCaseId", "approveLitigationCostRecord", "data", "batchApproveLitigationCostRecords", "getLitigationCostTypes", "getApprovalStatistics", "startLitigationApprovalFlow", "approveLitigationCostFlow", "rejectLitigationCostFlow", "batchApproveLitigationCostFlow"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/api/litigation_cost_approval/litigation_cost_approval.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）\nexport function listLitigationCostApproval(query) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询法诉费用审批详细\nexport function getLitigationCostApproval(id) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/' + id,\n    method: 'get'\n  })\n}\n\n// 获取法诉案件的费用提交记录详情（用于审批弹窗）\nexport function getLitigationCostSubmissionRecords(litigationCaseId) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/records/' + litigationCaseId,\n    method: 'get'\n  })\n}\n\n// 单个费用记录审批\nexport function approveLitigationCostRecord(data) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/approve',\n    method: 'put',\n    data: data\n  })\n}\n\n// 批量审批费用记录\nexport function batchApproveLitigationCostRecords(data) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/batchApprove',\n    method: 'put',\n    data: data\n  })\n}\n\n// 获取法诉费用类型字典\nexport function getLitigationCostTypes() {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/costTypes',\n    method: 'get'\n  })\n}\n\n// 获取审批状态统计\nexport function getApprovalStatistics() {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/statistics',\n    method: 'get'\n  })\n}\n\n// 开始审批流程\nexport function startLitigationApprovalFlow(id) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/startApproval/' + id,\n    method: 'post'\n  })\n}\n\n// 审批通过（进入下一个审批节点）\nexport function approveLitigationCostFlow(data) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/approveFlow',\n    method: 'put',\n    data: data\n  })\n}\n\n// 审批拒绝\nexport function rejectLitigationCostFlow(data) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/rejectFlow',\n    method: 'put',\n    data: data\n  })\n}\n\n// 批量审批流程\nexport function batchApproveLitigationCostFlow(data) {\n  return request({\n    url: '/litigation_cost_approval/litigation_cost_approval/batchApproveFlow',\n    method: 'put',\n    data: data\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,0BAA0BA,CAACC,KAAK,EAAE;EAChD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yDAAyD;IAC9DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,yBAAyBA,CAACC,EAAE,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qDAAqD,GAAGI,EAAE;IAC/DH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,kCAAkCA,CAACC,gBAAgB,EAAE;EACnE,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6DAA6D,GAAGM,gBAAgB;IACrFL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,4DAA4D;IACjEC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iCAAiCA,CAACD,IAAI,EAAE;EACtD,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,iEAAiE;IACtEC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,sBAAsBA,CAAA,EAAG;EACvC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,8DAA8D;IACnEC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,qBAAqBA,CAAA,EAAG;EACtC,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,+DAA+D;IACpEC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,2BAA2BA,CAACR,EAAE,EAAE;EAC9C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mEAAmE,GAAGI,EAAE;IAC7EH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,yBAAyBA,CAACL,IAAI,EAAE;EAC9C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,gEAAgE;IACrEC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,wBAAwBA,CAACN,IAAI,EAAE;EAC7C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,+DAA+D;IACpEC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,8BAA8BA,CAACP,IAAI,EAAE;EACnD,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,qEAAqE;IAC1EC,MAAM,EAAE,KAAK;IACbO,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}