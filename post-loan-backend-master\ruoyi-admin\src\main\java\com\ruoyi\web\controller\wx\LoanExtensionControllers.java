package com.ruoyi.web.controller.wx;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.loan_extension.domain.LoanExtension;
import com.ruoyi.loan_extension.service.ILoanExtensionService;
import com.ruoyi.loan_list.domain.LoanList;
import com.ruoyi.loan_list.service.ILoanListService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 流程延期申请Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/app/loan_extension")
public class LoanExtensionControllers extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(LoanExtensionControllers.class);
    @Autowired
    private ILoanExtensionService loanExtensionService;

    @Autowired
    private ILoanListService loanListService;

    /**
     * 查询流程延期申请列表
     */
    // @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:list')")
    @GetMapping("/list")
    public TableDataInfo list(LoanExtension loanExtension) {
        startPage();
        List<LoanExtension> list = loanExtensionService.selectLoanExtensionList(loanExtension);
        return getDataTable(list);
    }

    /**
     * 导出流程延期申请列表
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:export')")
    @Log(title = "流程延期申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LoanExtension loanExtension) {
        List<LoanExtension> list = loanExtensionService.selectLoanExtensionList(loanExtension);
        ExcelUtil<LoanExtension> util = new ExcelUtil<LoanExtension>(LoanExtension.class);
        util.exportExcel(response, list, "流程延期申请数据");
    }

    /**
     * 获取流程延期申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(loanExtensionService.selectLoanExtensionById(id));
    }

    /**
     * 新增流程延期申请
     */
    // @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:add')")
    @Log(title = "流程延期申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LoanExtension loanExtension) {
        // 先查询该loan是否已有申请中的延期流程
        if (loanExtension.getLoanId() != null) {
            LoanList loan = loanListService.selectLoanListById(String.valueOf(loanExtension.getLoanId()));
            if (loan != null && "1".equals(loan.getIsExtension())) {
                return AjaxResult.error("该还款已在申请延期中");
            }
        }
        loanExtension.setStatus(1);
        loanExtension.setCreateBy(getUsername());
        // 如果没有申请延期，则新建延期申请
        AjaxResult result = toAjax(loanExtensionService.insertLoanExtension(loanExtension));
        // 新建成功后改变loan延期申请状态
        if (result.isSuccess() && loanExtension.getLoanId() != null) {
            LoanList loan = new LoanList();
            loan.setId(loanExtension.getLoanId());
            loan.setIsExtension("1");
            loanListService.updateLoanList(loan);
        }
        return result;
    }

    /**
     * 修改流程延期申请
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:edit')")
    @Log(title = "流程延期申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LoanExtension loanExtension) {
        return toAjax(loanExtensionService.updateLoanExtension(loanExtension));
    }

    /**
     * 删除流程延期申请
     */
    @PreAuthorize("@ss.hasPermi('loan_extension:loan_extension:remove')")
    @Log(title = "流程延期申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(loanExtensionService.deleteLoanExtensionByIds(ids));
    }
}