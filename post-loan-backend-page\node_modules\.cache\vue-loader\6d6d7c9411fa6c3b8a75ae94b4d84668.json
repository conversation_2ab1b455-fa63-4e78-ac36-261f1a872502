{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationFeeForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\modules\\litigationFeeForm.vue", "mtime": 1754117642961}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBhZGRMaXRpZ2F0aW9uX2Nvc3QsIGNoZWNrU3VibWl0dGVkTGltaXRlZEZlZXMgfSBmcm9tICdAL2FwaS9saXRpZ2F0aW9uL2xpdGlnYXRpb24nDQppbXBvcnQgeyBhZGREYWlseV9leHBlbnNlX2FwcHJvdmFsLCBsaXN0RGFpbHlfZXhwZW5zZV9hcHByb3ZhbCB9IGZyb20gJ0AvYXBpL2RhaWx5X2V4cGVuc2VfYXBwcm92YWwvZGFpbHlfZXhwZW5zZV9hcHByb3ZhbCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnTGl0aWdhdGlvbkZlZUZvcm0nLA0KICBwcm9wczogew0KICAgIGRhdGE6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSksDQogICAgfSwNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBkYXRhOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgICBpZiAobmV3VmFsKSB7DQogICAgICAgICAgY29uc3QgY2FzZUlkID0gbmV3VmFsLuW6j+WPtw0KDQogICAgICAgICAgdGhpcy5saXRpZ2F0aW9uRmVlID0gew0KICAgICAgICAgICAgbGl0aWdhdGlvbkNhc2VJZDogY2FzZUlkID8gTnVtYmVyKGNhc2VJZCkgOiBudWxsLA0KICAgICAgICAgICAg6LS35qy+5Lq6OiBTdHJpbmcobmV3VmFsLui0t+asvuS6uiB8fCAnJyksDQogICAgICAgICAgICDlh7rljZXmuKDpgZM6IFN0cmluZyhuZXdWYWwu5Ye65Y2V5rig6YGTIHx8ICcnKSwNCiAgICAgICAgICAgIOaUvuasvumTtuihjDogU3RyaW5nKG5ld1ZhbC7mlL7mrL7pk7booYwgfHwgJycpLA0KICAgICAgICAgICAgdG90YWxNb25leTogMCwNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5saXRpZ2F0aW9uRmVlcyA9IFtdDQogICAgICAgICAgLy8g5qOA5p+l5bey5o+Q5Lqk55qE6ZmQ5Yi25oCn6LS555So57G75Z6LDQogICAgICAgICAgaWYgKGNhc2VJZCkgew0KICAgICAgICAgICAgdGhpcy5jaGVja1N1Ym1pdHRlZExpbWl0ZWRUeXBlcyhjYXNlSWQpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiB0cnVlLA0KICAgICAgZGVlcDogdHJ1ZSwNCiAgICB9LA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGxpdGlnYXRpb25GZWU6IHsNCiAgICAgICAgbGl0aWdhdGlvbkNhc2VJZDogJycsDQogICAgICAgIOi0t+asvuS6ujogJycsDQogICAgICAgIOWHuuWNlea4oOmBkzogJycsDQogICAgICAgIOaUvuasvumTtuihjDogJycsDQogICAgICAgIHRvdGFsTW9uZXk6IDAsDQogICAgICB9LCAvLyDms5Xor4notLnnlKjmj5DkuqTmlbDmja4NCiAgICAgIGxpdGlnYXRpb25GZWVzOiBbXSwgLy8g6K6w5b2V6LS555So55qE5YiX6KGoDQogICAgICBsaXRpZ2F0aW9uRmVlVHlwZU9wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJ+W+i+W4iOi0uScsIHZhbHVlOiAnbGF3eWVyRmVlJywgY2F0ZWdvcnk6ICdsaXRpZ2F0aW9uX2ZlZXMnLCBtdWx0aXBsZTogdHJ1ZSB9LA0KICAgICAgICB7IGxhYmVsOiAn5rOV6K+J6LS5JywgdmFsdWU6ICdsaXRpZ2F0aW9uRmVlJywgY2F0ZWdvcnk6ICdsaXRpZ2F0aW9uX2ZlZXMnLCBtdWx0aXBsZTogdHJ1ZSB9LA0KICAgICAgICB7IGxhYmVsOiAn5L+d5YWo6LS5JywgdmFsdWU6ICdwcmVzZXJ2YXRpb25GZWUnLCBjYXRlZ29yeTogJ2xpdGlnYXRpb25fZmVlcycsIG11bHRpcGxlOiB0cnVlIH0sDQogICAgICAgIHsgbGFiZWw6ICfluIPmjqfotLknLCB2YWx1ZTogJ3N1cnZlaWxsYW5jZUZlZScsIGNhdGVnb3J5OiAnbGl0aWdhdGlvbl9mZWVzJywgbXVsdGlwbGU6IHRydWUgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WFrOWRiui0uScsIHZhbHVlOiAnYW5ub3VuY2VtZW50RmVlJywgY2F0ZWdvcnk6ICdsaXRpZ2F0aW9uX2ZlZXMnLCBtdWx0aXBsZTogdHJ1ZSB9LA0KICAgICAgICB7IGxhYmVsOiAn6K+E5Lyw6LS5JywgdmFsdWU6ICdhcHByYWlzYWxGZWUnLCBjYXRlZ29yeTogJ2xpdGlnYXRpb25fZmVlcycsIG11bHRpcGxlOiB0cnVlIH0sDQogICAgICAgIHsgbGFiZWw6ICfmiafooYzotLknLCB2YWx1ZTogJ2V4ZWN1dGlvbkZlZScsIGNhdGVnb3J5OiAnbGl0aWdhdGlvbl9mZWVzJywgbXVsdGlwbGU6IHRydWUgfSwNCiAgICAgICAgeyBsYWJlbDogJ+i/nee6pumHkScsIHZhbHVlOiAncGVuYWx0eScsIGNhdGVnb3J5OiAnbGl0aWdhdGlvbl9mZWVzJywgbXVsdGlwbGU6IHRydWUgfSwNCiAgICAgICAgeyBsYWJlbDogJ+aLheS/nei0uScsIHZhbHVlOiAnZ3VhcmFudGVlRmVlJywgY2F0ZWdvcnk6ICdsaXRpZ2F0aW9uX2ZlZXMnLCBtdWx0aXBsZTogdHJ1ZSB9LA0KICAgICAgICB7IGxhYmVsOiAn5bGF6Ze06LS5JywgdmFsdWU6ICdpbnRlcm1lZGlhcnlGZWUnLCBjYXRlZ29yeTogJ2xpdGlnYXRpb25fZmVlcycsIG11bHRpcGxlOiB0cnVlIH0sDQogICAgICAgIHsgbGFiZWw6ICfku6Plgb/ph5EnLCB2YWx1ZTogJ2NvbXBlbnNpdHknLCBjYXRlZ29yeTogJ2xpdGlnYXRpb25fZmVlcycsIG11bHRpcGxlOiB0cnVlIH0sDQogICAgICAgIHsgbGFiZWw6ICfliKTlhrPph5Hpop0nLCB2YWx1ZTogJ2p1ZGdtZW50QW1vdW50JywgY2F0ZWdvcnk6ICdqdWRnbWVudF9pbnRlcmVzdCcsIG11bHRpcGxlOiBmYWxzZSB9LA0KICAgICAgICB7IGxhYmVsOiAn5Yip5oGvJywgdmFsdWU6ICdpbnRlcmVzdCcsIGNhdGVnb3J5OiAnanVkZ21lbnRfaW50ZXJlc3QnLCBtdWx0aXBsZTogZmFsc2UgfSwNCiAgICAgICAgeyBsYWJlbDogJ+WFtuS7luasoOasvicsIHZhbHVlOiAnb3RoZXJBbW91bnRzT3dlZCcsIGNhdGVnb3J5OiAnbGl0aWdhdGlvbl9mZWVzJywgbXVsdGlwbGU6IHRydWUgfSwNCiAgICAgICAgeyBsYWJlbDogJ+S/nemZqei0uScsIHZhbHVlOiAnaW5zdXJhbmNlJywgY2F0ZWdvcnk6ICdsaXRpZ2F0aW9uX2ZlZXMnLCBtdWx0aXBsZTogdHJ1ZSB9LA0KICAgICAgXSwNCiAgICAgIC8vIOW3suaPkOS6pOeahOmZkOWItuaAp+i0ueeUqOexu+Weiw0KICAgICAgc3VibWl0dGVkTGltaXRlZFR5cGVzOiBbXSwNCg0KICAgICAgLy8g5pel5bi46LS555So55Sz6K+355u45YWzDQogICAgICBkYWlseUV4cGVuc2VEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGRhaWx5RXhwZW5zZUZvcm06IHsNCiAgICAgICAgbGl0aWdhdGlvbkNhc2VJZDogJycsDQogICAgICAgIGxvYW5JZDogbnVsbCwNCiAgICAgICAgc3RhdHVzOiAxLCAvLyDms5Xor4nmj5DkuqQNCiAgICAgICAgZXhwZW5zZVR5cGU6ICcnLA0KICAgICAgICBleHBlbnNlQW1vdW50OiAnJywNCiAgICAgICAgZXhwZW5zZURhdGU6ICcnLA0KICAgICAgICBleHBlbnNlRGVzY3JpcHRpb246ICcnLA0KICAgICAgICBhcHBsaWNhbnRJZDogJycsDQogICAgICAgIGFwcGxpY2FudE5hbWU6ICcnLA0KICAgICAgICBhcHBsaWNhdGlvblRpbWU6ICcnLA0KICAgICAgICBhcHByb3ZhbFN0YXR1czogJzAnDQogICAgICB9LA0KICAgICAgZGFpbHlFeHBlbnNlUnVsZXM6IHsNCiAgICAgICAgZXhwZW5zZVR5cGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6LS555So57G75Z6LJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdLA0KICAgICAgICBleHBlbnNlQW1vdW50OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpei0ueeUqOmHkeminScsIHRyaWdnZXI6ICdibHVyJyB9LA0KICAgICAgICAgIHsgcGF0dGVybjogL15cZCsoXC5cZHsxLDJ9KT8kLywgbWVzc2FnZTogJ+ivt+i+k+WFpeato+ehrueahOmHkemineagvOW8jycsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIGV4cGVuc2VEYXRlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqei0ueeUqOWPkeeUn+aXpeacnycsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCg0KICAgICAgLy8g5pel5bi46LS555So5YiX6KGo5by556qXDQogICAgICBkYWlseUV4cGVuc2VMaXN0RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkYWlseUV4cGVuc2VMaXN0OiBbXSwNCiAgICAgIGRhaWx5RXhwZW5zZUxpc3RMb2FkaW5nOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOaWsOWinuazleiviei0ueeUqOihjA0KICAgIGFkZExpdGlnYXRpb25GZWUoKSB7DQogICAgICB0aGlzLmxpdGlnYXRpb25GZWVzLnB1c2goew0KICAgICAgICB0eXBlOiAnJywNCiAgICAgICAgYW1vdW50OiAnJywNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWIpOaWreafkOS4quexu+Wei+aYr+WQpuW3suiiq+WFtuWug+ihjOWNoOeUqOaIluiAheaYr+mZkOWItuaAp+exu+Wei+S4lOW3suaPkOS6pA0KICAgIGlzVHlwZURpc2FibGVkKHR5cGUsIGN1cnJlbnRJbmRleCkgew0KICAgICAgLy8g5qOA5p+l5piv5ZCm5Zyo5b2T5YmN6KGo5Y2V5Lit6YeN5aSNDQogICAgICBjb25zdCBpc0R1cGxpY2F0ZUluRm9ybSA9IHRoaXMubGl0aWdhdGlvbkZlZXMuc29tZSgoZmVlLCBpZHgpID0+IGZlZS50eXBlID09PSB0eXBlICYmIGlkeCAhPT0gY3VycmVudEluZGV4KQ0KDQogICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/pmZDliLbmgKfnsbvlnovkuJTlt7Lmj5DkuqTov4cNCiAgICAgIGNvbnN0IHR5cGVPcHRpb24gPSB0aGlzLmxpdGlnYXRpb25GZWVUeXBlT3B0aW9ucy5maW5kKG9wdGlvbiA9PiBvcHRpb24udmFsdWUgPT09IHR5cGUpDQogICAgICBjb25zdCBpc0xpbWl0ZWRBbmRTdWJtaXR0ZWQgPSB0eXBlT3B0aW9uICYmICF0eXBlT3B0aW9uLm11bHRpcGxlICYmIHRoaXMuc3VibWl0dGVkTGltaXRlZFR5cGVzLmluY2x1ZGVzKHR5cGUpDQoNCiAgICAgIHJldHVybiBpc0R1cGxpY2F0ZUluRm9ybSB8fCBpc0xpbWl0ZWRBbmRTdWJtaXR0ZWQNCiAgICB9LA0KDQogICAgLy8g6YCJ5Lit5ZCO5qCh6aqM77ya6Iul6YeN5aSN5oiW6ZmQ5Yi25oCn57G75Z6L5bey5o+Q5Lqk5YiZ5riF56m6DQogICAgaGFuZGxlTGl0aWdhdGlvbkZlZVR5cGVDaGFuZ2UoaXRlbSwgaW5kZXgpIHsNCiAgICAgIGNvbnN0IGR1cGxpY2F0ZSA9IHRoaXMubGl0aWdhdGlvbkZlZXMuc29tZSgoZmVlLCBpZHgpID0+IGZlZS50eXBlID09PSBpdGVtLnR5cGUgJiYgaWR4ICE9PSBpbmRleCkNCiAgICAgIGlmIChkdXBsaWNhdGUpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor6XotLnnlKjnsbvlnovlt7LpgInmi6nvvIzor7fli7/ph43lpI3vvIEnKQ0KICAgICAgICB0aGlzLiRzZXQodGhpcy5saXRpZ2F0aW9uRmVlc1tpbmRleF0sICd0eXBlJywgJycpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/pmZDliLbmgKfnsbvlnovkuJTlt7Lmj5DkuqQNCiAgICAgIGNvbnN0IHR5cGVPcHRpb24gPSB0aGlzLmxpdGlnYXRpb25GZWVUeXBlT3B0aW9ucy5maW5kKG9wdGlvbiA9PiBvcHRpb24udmFsdWUgPT09IGl0ZW0udHlwZSkNCiAgICAgIGlmICh0eXBlT3B0aW9uICYmICF0eXBlT3B0aW9uLm11bHRpcGxlICYmIHRoaXMuc3VibWl0dGVkTGltaXRlZFR5cGVzLmluY2x1ZGVzKGl0ZW0udHlwZSkpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGAke3R5cGVPcHRpb24ubGFiZWx95Y+q6IO95o+Q5Lqk5LiA5qyh77yM5bey57uP5o+Q5Lqk6L+H5LqG77yBYCkNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubGl0aWdhdGlvbkZlZXNbaW5kZXhdLCAndHlwZScsICcnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5piv5pel5bi45oql6ZSA57G75Z6L77yM5o+Q56S655So5oi35bqU6K+l5Zyo5pel5bi46LS555So5a6h5om55Lit5o+Q5LqkDQogICAgICBpZiAoaXRlbS50eXBlID09PSAnZGFpbHlSZWltYnVyc2VtZW50Jykgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aXpeW4uOaKpemUgOi0ueeUqOivt+WcqCLml6XluLjotLnnlKjlrqHmibki5qih5Z2X5Lit5o+Q5Lqk77yBJykNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMubGl0aWdhdGlvbkZlZXNbaW5kZXhdLCAndHlwZScsICcnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6YeR6aKd5Y+Y5YyW5pe26YeN5paw6K6h566X5oC76YeR6aKdDQogICAgaGFuZGxlTGl0aWdhdGlvbkZlZUFtb3VudENoYW5nZSgpIHsNCiAgICAgIHRoaXMubGl0aWdhdGlvbkZlZS50b3RhbE1vbmV5ID0gdGhpcy5saXRpZ2F0aW9uRmVlcy5yZWR1Y2UoKHN1bSwgZmVlKSA9PiBzdW0gKyBOdW1iZXIoZmVlLmFtb3VudCB8fCAwKSwgMCkNCiAgICB9LA0KDQogICAgLy8g5Yig6Zmk5rOV6K+J6LS555So6KGMDQogICAgcmVtb3ZlTGl0aWdhdGlvbkZlZShpbmRleCkgew0KICAgICAgdGhpcy5saXRpZ2F0aW9uRmVlcy5zcGxpY2UoaW5kZXgsIDEpDQogICAgICB0aGlzLmhhbmRsZUxpdGlnYXRpb25GZWVBbW91bnRDaGFuZ2UoKQ0KICAgIH0sDQoNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgLy8g6aqM6K+B5piv5ZCm5pyJ6LS555So6aG555uuDQogICAgICBpZiAodGhpcy5saXRpZ2F0aW9uRmVlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7foh7PlsJHmt7vliqDkuIDpobnotLnnlKjvvIEnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g6aqM6K+B5omA5pyJ6LS555So6aG555uu5piv5ZCm5a6M5pW0DQogICAgICBjb25zdCBoYXNJbmNvbXBsZXRlSXRlbSA9IHRoaXMubGl0aWdhdGlvbkZlZXMuc29tZShmZWUgPT4gIWZlZS50eXBlIHx8ICFmZWUuYW1vdW50KQ0KICAgICAgaWYgKGhhc0luY29tcGxldGVJdGVtKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35a6M5ZaE5omA5pyJ6LS555So6aG555uu55qE57G75Z6L5ZKM6YeR6aKd77yBJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOWIhuemu+aXpeW4uOi0ueeUqOWSjOWFtuS7lui0ueeUqA0KICAgICAgY29uc3QgZGFpbHlFeHBlbnNlcyA9IHRoaXMubGl0aWdhdGlvbkZlZXMuZmlsdGVyKGZlZSA9PiBmZWUudHlwZSA9PT0gJ2RhaWx5UmVpbWJ1cnNlbWVudCcpDQogICAgICBjb25zdCBvdGhlckZlZXMgPSB0aGlzLmxpdGlnYXRpb25GZWVzLmZpbHRlcihmZWUgPT4gZmVlLnR5cGUgIT09ICdkYWlseVJlaW1idXJzZW1lbnQnKQ0KDQogICAgICAvLyDlpoLmnpzmnInml6XluLjotLnnlKjvvIzmj5DnpLrnlKjmiLcNCiAgICAgIGlmIChkYWlseUV4cGVuc2VzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmo4DmtYvliLDml6XluLjmiqXplIDotLnnlKjvvIzor7flnKgi5pel5bi46LS555So5a6h5om5IuaooeWdl+S4reaPkOS6pO+8gScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDmnoTlu7rmj5DkuqTmlbDmja4NCiAgICAgIGNvbnN0IGxpdGlnYXRpb25GZWUgPSB7IC4uLnRoaXMubGl0aWdhdGlvbkZlZSB9DQoNCiAgICAgIC8vIOehruS/nSBsaXRpZ2F0aW9uQ2FzZUlkIOaYr+aVsOWtl+exu+Weiw0KICAgICAgaWYgKGxpdGlnYXRpb25GZWUubGl0aWdhdGlvbkNhc2VJZCkgew0KICAgICAgICBsaXRpZ2F0aW9uRmVlLmxpdGlnYXRpb25DYXNlSWQgPSBOdW1iZXIobGl0aWdhdGlvbkZlZS5saXRpZ2F0aW9uQ2FzZUlkKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign57y65bCR5rOV6K+J5qGI5Lu2SUTvvIzor7fph43mlrDpgInmi6nmoYjku7YnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5bCG6LS555So5YiX6KGo5Lit55qE5pWw5o2u5Yqo5oCB5aGr5YWl5YiwIGxpdGlnYXRpb25GZWUg5a+56LGh5LitDQogICAgICBvdGhlckZlZXMuZm9yRWFjaCgoeyB0eXBlLCBhbW91bnQgfSkgPT4gew0KICAgICAgICBpZiAodHlwZSkgbGl0aWdhdGlvbkZlZVt0eXBlXSA9IGFtb3VudCB8fCAwDQogICAgICB9KQ0KDQogICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk55qE5rOV6K+J6LS555So5pWw5o2uOicsIGxpdGlnYXRpb25GZWUpDQoNCiAgICAgIC8vIOiwg+eUqCBBUEkg5o+Q5Lqk5pWw5o2uDQogICAgICBhZGRMaXRpZ2F0aW9uX2Nvc3QobGl0aWdhdGlvbkZlZSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgICAgIC8vIOWIt+aWsOeItue7hOS7tuaVsOaNrg0KICAgICAgICAgIHRoaXMuJGVtaXQoJ3JlZnJlc2gnKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aPkOS6pOWksei0pe+8micgKyAocmVzLm1zZyB8fCAn5pyq55+l6ZSZ6K+vJykpDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5o+Q5Lqk5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmj5DkuqTlpLHotKXvvIzor7fnqI3lkI7ph43or5UnKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQoNCiAgICBvcGVuKCkgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgfSwNCg0KICAgIC8vIOajgOafpeW3suaPkOS6pOeahOmZkOWItuaAp+i0ueeUqOexu+Weiw0KICAgIGNoZWNrU3VibWl0dGVkTGltaXRlZFR5cGVzKGxpdGlnYXRpb25DYXNlSWQpIHsNCiAgICAgIGlmICghbGl0aWdhdGlvbkNhc2VJZCkgcmV0dXJuDQoNCiAgICAgIC8vIOiwg+eUqOWQjuerr0FQSeajgOafpeW3suaPkOS6pOeahOWIpOWGs+mHkemineWSjOWIqeaBrw0KICAgICAgY2hlY2tTdWJtaXR0ZWRMaW1pdGVkRmVlcyhsaXRpZ2F0aW9uQ2FzZUlkKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5zdWJtaXR0ZWRMaW1pdGVkVHlwZXMgPSByZXMuZGF0YSB8fCBbXQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ajgOafpemZkOWItuaAp+i0ueeUqOexu+Wei+Wksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5zdWJtaXR0ZWRMaW1pdGVkVHlwZXMgPSBbXQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5omT5byA5pel5bi46LS555So55Sz6K+35by556qXDQogICAgb3BlbkRhaWx5RXhwZW5zZURpYWxvZyhsaXRpZ2F0aW9uQ2FzZUlkID0gbnVsbCkgew0KICAgICAgdGhpcy5yZXNldERhaWx5RXhwZW5zZUZvcm0oKQ0KICAgICAgdGhpcy5kYWlseUV4cGVuc2VGb3JtLmxpdGlnYXRpb25DYXNlSWQgPSBTdHJpbmcobGl0aWdhdGlvbkNhc2VJZCB8fCB0aGlzLmxpdGlnYXRpb25GZWUubGl0aWdhdGlvbkNhc2VJZCB8fCAnJykNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlRm9ybS5zdGF0dXMgPSAxIC8vIOazleivieaPkOS6pA0KICAgICAgdGhpcy5kYWlseUV4cGVuc2VGb3JtLmFwcGxpY2FudElkID0gU3RyaW5nKHRoaXMuJHN0b3JlLnN0YXRlLnVzZXI/LmlkIHx8ICcnKQ0KICAgICAgdGhpcy5kYWlseUV4cGVuc2VGb3JtLmFwcGxpY2FudE5hbWUgPSBTdHJpbmcodGhpcy4kc3RvcmUuc3RhdGUudXNlcj8ubmFtZSB8fCAnJykNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlRm9ybS5hcHBsaWNhdGlvblRpbWUgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXQ0KICAgICAgdGhpcy5kYWlseUV4cGVuc2VEaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQoNCiAgICAvLyDlj5bmtojml6XluLjotLnnlKjnlLPor7cNCiAgICBjYW5jZWxEYWlseUV4cGVuc2UoKSB7DQogICAgICB0aGlzLmRhaWx5RXhwZW5zZURpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldERhaWx5RXhwZW5zZUZvcm0oKQ0KICAgIH0sDQoNCiAgICAvLyDmj5DkuqTml6XluLjotLnnlKjnlLPor7cNCiAgICBzdWJtaXREYWlseUV4cGVuc2UoKSB7DQogICAgICB0aGlzLiRyZWZzLmRhaWx5RXhwZW5zZUZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOazleivieaPkOS6pO+8muehruS/neaciSBsaXRpZ2F0aW9uQ2FzZUlkIOWSjCBzdGF0dXMgPSAxDQogICAgICAgICAgY29uc3Qgc3VibWl0RGF0YSA9IHsgLi4udGhpcy5kYWlseUV4cGVuc2VGb3JtIH0NCiAgICAgICAgICBpZiAoIXN1Ym1pdERhdGEubGl0aWdhdGlvbkNhc2VJZCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign57y65bCR5rOV6K+J5qGI5Lu2SUTvvIzor7fph43mlrDpgInmi6nmoYjku7YnKQ0KICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgfQ0KICAgICAgICAgIHN1Ym1pdERhdGEuc3RhdHVzID0gMSAvLyDnoa7kv53mmK/ms5Xor4nmj5DkuqQNCiAgICAgICAgICBhZGREYWlseV9leHBlbnNlX2FwcHJvdmFsKHN1Ym1pdERhdGEpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pel5bi46LS555So55Sz6K+35o+Q5Lqk5oiQ5YqfJykNCiAgICAgICAgICAgICAgdGhpcy5kYWlseUV4cGVuc2VEaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5yZXNldERhaWx5RXhwZW5zZUZvcm0oKQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5o+Q5Lqk5aSx6LSl77yaJyArIChyZXMubXNnIHx8ICfmnKrnn6XplJnor68nKSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmj5DkuqTlpLHotKU6JywgZXJyb3IpDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmj5DkuqTlpLHotKXvvIzor7fnqI3lkI7ph43or5UnKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KDQoNCiAgICAvLyDph43nva7ml6XluLjotLnnlKjooajljZUNCiAgICByZXNldERhaWx5RXhwZW5zZUZvcm0oKSB7DQogICAgICB0aGlzLmRhaWx5RXhwZW5zZUZvcm0gPSB7DQogICAgICAgIGxpdGlnYXRpb25DYXNlSWQ6ICcnLA0KICAgICAgICBsb2FuSWQ6IG51bGwsDQogICAgICAgIHN0YXR1czogMSwgLy8g5rOV6K+J5o+Q5LqkDQogICAgICAgIGV4cGVuc2VUeXBlOiAnJywNCiAgICAgICAgZXhwZW5zZUFtb3VudDogJycsDQogICAgICAgIGV4cGVuc2VEYXRlOiAnJywNCiAgICAgICAgZXhwZW5zZURlc2NyaXB0aW9uOiAnJywNCiAgICAgICAgYXBwbGljYW50SWQ6ICcnLA0KICAgICAgICBhcHBsaWNhbnROYW1lOiAnJywNCiAgICAgICAgYXBwbGljYXRpb25UaW1lOiAnJywNCiAgICAgICAgYXBwcm92YWxTdGF0dXM6ICcwJw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuJHJlZnMuZGFpbHlFeHBlbnNlRm9ybSkgew0KICAgICAgICB0aGlzLiRyZWZzLmRhaWx5RXhwZW5zZUZvcm0ucmVzZXRGaWVsZHMoKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICByZXNldCgpIHsNCiAgICAgIC8vIOS/neeVmSBsaXRpZ2F0aW9uQ2FzZUlk77yM5Y+q6YeN572u5YW25LuW5a2X5q61DQogICAgICBjb25zdCBjdXJyZW50TGl0aWdhdGlvbkNhc2VJZCA9IHRoaXMubGl0aWdhdGlvbkZlZS5saXRpZ2F0aW9uQ2FzZUlkDQogICAgICB0aGlzLmxpdGlnYXRpb25GZWUgPSB7DQogICAgICAgIGxpdGlnYXRpb25DYXNlSWQ6IGN1cnJlbnRMaXRpZ2F0aW9uQ2FzZUlkLA0KICAgICAgICDotLfmrL7kuro6IHRoaXMubGl0aWdhdGlvbkZlZS7otLfmrL7kurosDQogICAgICAgIOWHuuWNlea4oOmBkzogdGhpcy5saXRpZ2F0aW9uRmVlLuWHuuWNlea4oOmBkywNCiAgICAgICAg5pS+5qy+6ZO26KGMOiB0aGlzLmxpdGlnYXRpb25GZWUu5pS+5qy+6ZO26KGMLA0KICAgICAgICB0b3RhbE1vbmV5OiAwLA0KICAgICAgfQ0KICAgICAgdGhpcy5saXRpZ2F0aW9uRmVlcyA9IFtdDQogICAgICAvLyDkuI3ph43nva4gc3VibWl0dGVkTGltaXRlZFR5cGVz77yM5Zug5Li66L+Z5piv5LuO5ZCO56uv6I635Y+W55qE54q25oCBDQogICAgfSwNCg0KICAgIC8vIOafpeeci+aXpeW4uOi0ueeUqOWIl+ihqA0KICAgIHZpZXdEYWlseUV4cGVuc2VMaXN0KCkgew0KICAgICAgdGhpcy5kYWlseUV4cGVuc2VMaXN0TG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMuZGFpbHlFeHBlbnNlTGlzdERpYWxvZ1Zpc2libGUgPSB0cnVlDQoNCiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBsaXRpZ2F0aW9uQ2FzZUlkOiB0aGlzLmxpdGlnYXRpb25GZWUubGl0aWdhdGlvbkNhc2VJZCwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwMA0KICAgICAgfQ0KDQogICAgICBsaXN0RGFpbHlfZXhwZW5zZV9hcHByb3ZhbChxdWVyeVBhcmFtcykudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZGFpbHlFeHBlbnNlTGlzdCA9IHJlcy5yb3dzIHx8IFtdDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5pel5bi46LS555So5YiX6KGo5aSx6LSl77yaJyArIChyZXMubXNnIHx8ICfmnKrnn6XplJnor68nKSkNCiAgICAgICAgICB0aGlzLmRhaWx5RXhwZW5zZUxpc3QgPSBbXQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuZGFpbHlFeHBlbnNlTGlzdExvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bml6XluLjotLnnlKjliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaXpeW4uOi0ueeUqOWIl+ihqOWksei0pe+8jOivt+eojeWQjumHjeivlScpDQogICAgICAgIHRoaXMuZGFpbHlFeHBlbnNlTGlzdCA9IFtdDQogICAgICAgIHRoaXMuZGFpbHlFeHBlbnNlTGlzdExvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g6I635Y+W6LS555So57G75Z6L5qCH562+DQogICAgZ2V0RXhwZW5zZVR5cGVMYWJlbCh0eXBlKSB7DQogICAgICBjb25zdCB0eXBlTWFwID0gew0KICAgICAgICAnb2lsX2ZlZSc6ICfmsrnotLknLA0KICAgICAgICAncm9hZF9mZWUnOiAn6Lev6LS5JywNCiAgICAgICAgJ21lYWxfZmVlJzogJ+mkkOi0uScsDQogICAgICAgICdhY2NvbW1vZGF0aW9uX2ZlZSc6ICfkvY/lrr/otLknLA0KICAgICAgICAndHJhbnNwb3J0X2ZlZSc6ICfkuqTpgJrotLknLA0KICAgICAgICAncGFya2luZ19mZWUnOiAn5YGc6L2m6LS5JywNCiAgICAgICAgJ2NvbW11bmljYXRpb25fZmVlJzogJ+mAmuiur+i0uScsDQogICAgICAgICdvdGhlcic6ICflhbbku5YnDQogICAgICB9DQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCB0eXBlDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWuoeaJueeKtuaAgeaWh+acrA0KICAgIGdldFN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICcwJzogJ+W+heWuoeaJuScsDQogICAgICAgICcxJzogJ+WFqOmDqOmAmui/hycsDQogICAgICAgICcyJzogJ+W3suaLkue7nScsDQogICAgICAgICczJzogJ+S4u+euoeWuoeaJueS4rScsDQogICAgICAgICc0JzogJ+aAu+ebkeWuoeaJueS4rScsDQogICAgICAgICc1JzogJ+i0ouWKoeS4u+euoeWuoeaJueS4rScsDQogICAgICAgICc2JzogJ+aAu+e7j+eQhuWuoeaJueS4rScNCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAn5pyq55+l54q25oCBJw0KICAgIH0sDQoNCiAgICAvLyDojrflj5blrqHmibnnirbmgIHmoIfnrb7nsbvlnosNCiAgICBnZXRTdGF0dXNUYWdUeXBlKHN0YXR1cykgew0KICAgICAgY29uc3QgdHlwZU1hcCA9IHsNCiAgICAgICAgJzAnOiAnd2FybmluZycsICAgIC8vIOW+heWuoeaJuSAtIOapmeiJsg0KICAgICAgICAnMSc6ICdzdWNjZXNzJywgICAgLy8g5YWo6YOo6YCa6L+HIC0g57u/6ImyDQogICAgICAgICcyJzogJ2RhbmdlcicsICAgICAvLyDlt7Lmi5Lnu50gLSDnuqLoibINCiAgICAgICAgJzMnOiAncHJpbWFyeScsICAgIC8vIOS4u+euoeWuoeaJueS4rSAtIOiTneiJsg0KICAgICAgICAnNCc6ICdwcmltYXJ5JywgICAgLy8g5oC755uR5a6h5om55LitIC0g6JOd6ImyDQogICAgICAgICc1JzogJ3ByaW1hcnknLCAgICAvLyDotKLliqHkuLvnrqHlrqHmibnkuK0gLSDok53oibINCiAgICAgICAgJzYnOiAncHJpbWFyeScgICAgIC8vIOaAu+e7j+eQhuWuoeaJueS4rSAtIOiTneiJsg0KICAgICAgfQ0KICAgICAgcmV0dXJuIHR5cGVNYXBbc3RhdHVzXSB8fCAnaW5mbycNCiAgICB9DQogIH0sDQp9DQo="}, {"version": 3, "sources": ["litigationFeeForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigationFeeForm.vue", "sourceRoot": "src/views/litigation/litigation/modules", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 法诉费用提交弹窗 -->\r\n    <el-dialog title=\"提交法诉费用\" :visible.sync=\"dialogVisible\" width=\"800px\" append-to-body>\r\n    <el-descriptions :column=\"1\" border style=\"margin-bottom: 20px\">\r\n      <el-descriptions-item label=\"贷款人\">{{ litigationFee.贷款人 }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"出单渠道\">{{ litigationFee.出单渠道 }}</el-descriptions-item>\r\n      <el-descriptions-item label=\"放款银行\">{{ litigationFee.放款银行 }}</el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <div style=\"padding: 0 20px\">\r\n      <!-- 费用提交规则提示 -->\r\n      <div class=\"fee-rules-container\">\r\n        <div class=\"fee-rules-header\">\r\n          <i class=\"el-icon-info\"></i>\r\n          <span class=\"fee-rules-title\">费用提交规则</span>\r\n        </div>\r\n        <div class=\"fee-rules-content\">\r\n          <div class=\"rule-item\">\r\n            <span class=\"rule-bullet\">•</span>\r\n            <span><strong>判决金额</strong>和<strong>利息</strong>每个案件只能提交一次</span>\r\n          </div>\r\n          <div class=\"rule-item\">\r\n            <span class=\"rule-bullet\">•</span>\r\n            <span><strong>日常费用</strong>（如油费、路费、餐费等）请在\"日常费用审批\"模块中提交</span>\r\n          </div>\r\n          <div class=\"rule-item\">\r\n            <span class=\"rule-bullet\">•</span>\r\n            <span>其他诉讼费用可以多次提交</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"margin-bottom: 20px\">\r\n        <div style=\"display: flex; align-items: flex-start\">\r\n          <label style=\"width: 120px; text-align: right; margin-right: 10px; font-weight: bold; line-height: 32px; flex-shrink: 0\">法诉费用：</label>\r\n          <div style=\"flex: 1\">\r\n            <div v-for=\"(item, index) in litigationFees\" :key=\"index\" style=\"margin-bottom: 10px\">\r\n              <el-row :gutter=\"10\" type=\"flex\" align=\"middle\">\r\n                <el-col :span=\"8\">\r\n                  <el-select\r\n                    v-model=\"item.type\"\r\n                    placeholder=\"请选择法诉费用类型\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleLitigationFeeTypeChange(item, index)\">\r\n                    <el-option\r\n                      v-for=\"option in litigationFeeTypeOptions\"\r\n                      :key=\"option.value\"\r\n                      :label=\"option.label\"\r\n                      :value=\"option.value\"\r\n                      :disabled=\"isTypeDisabled(option.value, index)\" />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-input\r\n                    v-model=\"item.amount\"\r\n                    placeholder=\"请输入金额\"\r\n                    @input=\"handleLitigationFeeAmountChange\"\r\n                    :disabled=\"item.type === ''\">\r\n                    <template slot=\"prepend\">￥</template>\r\n                  </el-input>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-button type=\"danger\" icon=\"el-icon-delete\" style=\"width: 54px\" @click=\"removeLitigationFee(index)\" />\r\n                </el-col>\r\n              </el-row>\r\n            </div>\r\n\r\n            <!-- 新增按钮 -->\r\n            <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addLitigationFee\">新增法诉费用</el-button>\r\n            <el-button type=\"success\" size=\"small\" icon=\"el-icon-document\" @click=\"openDailyExpenseDialog\" style=\"margin-left: 10px\">申请日常费用</el-button>\r\n            <el-button type=\"info\" size=\"small\" icon=\"el-icon-view\" @click=\"viewDailyExpenseList\" style=\"margin-left: 10px\">查看日常费用</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div style=\"margin-bottom: 20px\">\r\n        <label style=\"display: inline-block; width: 120px; text-align: right; margin-right: 10px; font-weight: bold\">合计法诉费用：</label>\r\n        <div style=\"display: inline-block; width: calc(100% - 130px)\">\r\n          <el-input v-model=\"litigationFee.totalMoney\" placeholder=\"请输入合计法诉费用\" disabled>\r\n            <template slot=\"prepend\">￥</template>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n\r\n  <!-- 日常费用申请弹窗 -->\r\n  <el-dialog title=\"申请日常费用\" :visible.sync=\"dailyExpenseDialogVisible\" width=\"600px\" append-to-body>\r\n    <el-form ref=\"dailyExpenseForm\" :model=\"dailyExpenseForm\" :rules=\"dailyExpenseRules\" label-width=\"120px\">\r\n      <el-form-item label=\"费用类型\" prop=\"expenseType\">\r\n        <el-select v-model=\"dailyExpenseForm.expenseType\" placeholder=\"请选择费用类型\" style=\"width: 100%\">\r\n          <el-option label=\"油费\" value=\"oil_fee\" />\r\n          <el-option label=\"路费\" value=\"road_fee\" />\r\n          <el-option label=\"餐费\" value=\"meal_fee\" />\r\n          <el-option label=\"住宿费\" value=\"accommodation_fee\" />\r\n          <el-option label=\"交通费\" value=\"transport_fee\" />\r\n          <el-option label=\"停车费\" value=\"parking_fee\" />\r\n          <el-option label=\"通讯费\" value=\"communication_fee\" />\r\n          <el-option label=\"其他\" value=\"other\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"费用金额\" prop=\"expenseAmount\">\r\n        <el-input v-model=\"dailyExpenseForm.expenseAmount\" placeholder=\"请输入费用金额\">\r\n          <template slot=\"prepend\">￥</template>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"费用发生日期\" prop=\"expenseDate\">\r\n        <el-date-picker\r\n          v-model=\"dailyExpenseForm.expenseDate\"\r\n          type=\"date\"\r\n          placeholder=\"请选择费用发生日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          style=\"width: 100%\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"费用说明\" prop=\"expenseDescription\">\r\n        <el-input v-model=\"dailyExpenseForm.expenseDescription\" type=\"textarea\" placeholder=\"请输入费用说明\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"cancelDailyExpense\">取 消</el-button>\r\n      <el-button type=\"primary\" @click=\"submitDailyExpense\">确 定</el-button>\r\n    </div>\r\n  </el-dialog>\r\n\r\n  <!-- 查看日常费用列表弹窗 -->\r\n  <el-dialog title=\"日常费用申请记录\" :visible.sync=\"dailyExpenseListDialogVisible\" width=\"800px\" append-to-body>\r\n    <el-table :data=\"dailyExpenseList\" v-loading=\"dailyExpenseListLoading\">\r\n      <el-table-column label=\"费用类型\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getExpenseTypeLabel(scope.row.expenseType) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"费用金额\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          ￥{{ scope.row.expenseAmount }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"费用发生日期\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.expenseDate }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"申请时间\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.applicationTime }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批状态\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\r\n            {{ getStatusText(scope.row.approvalStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"费用说明\" align=\"center\" prop=\"expenseDescription\" />\r\n    </el-table>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button @click=\"dailyExpenseListDialogVisible = false\">关 闭</el-button>\r\n    </div>\r\n  </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style scoped>\r\n.fee-rules-container {\r\n  background-color: #f4f4f5;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.fee-rules-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.fee-rules-header .el-icon-info {\r\n  color: #409EFF;\r\n  font-size: 16px;\r\n  margin-right: 8px;\r\n}\r\n\r\n.fee-rules-title {\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.fee-rules-content {\r\n  padding-left: 24px;\r\n}\r\n\r\n.rule-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 8px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.rule-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.rule-bullet {\r\n  color: #409EFF;\r\n  margin-right: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.rule-item span {\r\n  color: #606266;\r\n  font-size: 13px;\r\n}\r\n\r\n.rule-item strong {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { addLitigation_cost, checkSubmittedLimitedFees } from '@/api/litigation/litigation'\r\nimport { addDaily_expense_approval, listDaily_expense_approval } from '@/api/daily_expense_approval/daily_expense_approval'\r\n\r\nexport default {\r\n  name: 'LitigationFeeForm',\r\n  props: {\r\n    data: {\r\n      type: Object,\r\n      default: () => ({}),\r\n    },\r\n  },\r\n  watch: {\r\n    data: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          const caseId = newVal.序号\r\n\r\n          this.litigationFee = {\r\n            litigationCaseId: caseId ? Number(caseId) : null,\r\n            贷款人: String(newVal.贷款人 || ''),\r\n            出单渠道: String(newVal.出单渠道 || ''),\r\n            放款银行: String(newVal.放款银行 || ''),\r\n            totalMoney: 0,\r\n          }\r\n          this.litigationFees = []\r\n          // 检查已提交的限制性费用类型\r\n          if (caseId) {\r\n            this.checkSubmittedLimitedTypes(caseId)\r\n          }\r\n        }\r\n      },\r\n      immediate: true,\r\n      deep: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      litigationFee: {\r\n        litigationCaseId: '',\r\n        贷款人: '',\r\n        出单渠道: '',\r\n        放款银行: '',\r\n        totalMoney: 0,\r\n      }, // 法诉费用提交数据\r\n      litigationFees: [], // 记录费用的列表\r\n      litigationFeeTypeOptions: [\r\n        { label: '律师费', value: 'lawyerFee', category: 'litigation_fees', multiple: true },\r\n        { label: '法诉费', value: 'litigationFee', category: 'litigation_fees', multiple: true },\r\n        { label: '保全费', value: 'preservationFee', category: 'litigation_fees', multiple: true },\r\n        { label: '布控费', value: 'surveillanceFee', category: 'litigation_fees', multiple: true },\r\n        { label: '公告费', value: 'announcementFee', category: 'litigation_fees', multiple: true },\r\n        { label: '评估费', value: 'appraisalFee', category: 'litigation_fees', multiple: true },\r\n        { label: '执行费', value: 'executionFee', category: 'litigation_fees', multiple: true },\r\n        { label: '违约金', value: 'penalty', category: 'litigation_fees', multiple: true },\r\n        { label: '担保费', value: 'guaranteeFee', category: 'litigation_fees', multiple: true },\r\n        { label: '居间费', value: 'intermediaryFee', category: 'litigation_fees', multiple: true },\r\n        { label: '代偿金', value: 'compensity', category: 'litigation_fees', multiple: true },\r\n        { label: '判决金额', value: 'judgmentAmount', category: 'judgment_interest', multiple: false },\r\n        { label: '利息', value: 'interest', category: 'judgment_interest', multiple: false },\r\n        { label: '其他欠款', value: 'otherAmountsOwed', category: 'litigation_fees', multiple: true },\r\n        { label: '保险费', value: 'insurance', category: 'litigation_fees', multiple: true },\r\n      ],\r\n      // 已提交的限制性费用类型\r\n      submittedLimitedTypes: [],\r\n\r\n      // 日常费用申请相关\r\n      dailyExpenseDialogVisible: false,\r\n      dailyExpenseForm: {\r\n        litigationCaseId: '',\r\n        loanId: null,\r\n        status: 1, // 法诉提交\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      },\r\n      dailyExpenseRules: {\r\n        expenseType: [\r\n          { required: true, message: '请选择费用类型', trigger: 'change' }\r\n        ],\r\n        expenseAmount: [\r\n          { required: true, message: '请输入费用金额', trigger: 'blur' },\r\n          { pattern: /^\\d+(\\.\\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }\r\n        ],\r\n        expenseDate: [\r\n          { required: true, message: '请选择费用发生日期', trigger: 'change' }\r\n        ]\r\n      },\r\n\r\n      // 日常费用列表弹窗\r\n      dailyExpenseListDialogVisible: false,\r\n      dailyExpenseList: [],\r\n      dailyExpenseListLoading: false\r\n    }\r\n  },\r\n  methods: {\r\n    // 新增法诉费用行\r\n    addLitigationFee() {\r\n      this.litigationFees.push({\r\n        type: '',\r\n        amount: '',\r\n      })\r\n    },\r\n\r\n    // 判断某个类型是否已被其它行占用或者是限制性类型且已提交\r\n    isTypeDisabled(type, currentIndex) {\r\n      // 检查是否在当前表单中重复\r\n      const isDuplicateInForm = this.litigationFees.some((fee, idx) => fee.type === type && idx !== currentIndex)\r\n\r\n      // 检查是否是限制性类型且已提交过\r\n      const typeOption = this.litigationFeeTypeOptions.find(option => option.value === type)\r\n      const isLimitedAndSubmitted = typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(type)\r\n\r\n      return isDuplicateInForm || isLimitedAndSubmitted\r\n    },\r\n\r\n    // 选中后校验：若重复或限制性类型已提交则清空\r\n    handleLitigationFeeTypeChange(item, index) {\r\n      const duplicate = this.litigationFees.some((fee, idx) => fee.type === item.type && idx !== index)\r\n      if (duplicate) {\r\n        this.$message.warning('该费用类型已选择，请勿重复！')\r\n        this.$set(this.litigationFees[index], 'type', '')\r\n        return\r\n      }\r\n\r\n      // 检查是否是限制性类型且已提交\r\n      const typeOption = this.litigationFeeTypeOptions.find(option => option.value === item.type)\r\n      if (typeOption && !typeOption.multiple && this.submittedLimitedTypes.includes(item.type)) {\r\n        this.$message.warning(`${typeOption.label}只能提交一次，已经提交过了！`)\r\n        this.$set(this.litigationFees[index], 'type', '')\r\n        return\r\n      }\r\n\r\n      // 如果是日常报销类型，提示用户应该在日常费用审批中提交\r\n      if (item.type === 'dailyReimbursement') {\r\n        this.$message.warning('日常报销费用请在\"日常费用审批\"模块中提交！')\r\n        this.$set(this.litigationFees[index], 'type', '')\r\n        return\r\n      }\r\n    },\r\n\r\n    // 金额变化时重新计算总金额\r\n    handleLitigationFeeAmountChange() {\r\n      this.litigationFee.totalMoney = this.litigationFees.reduce((sum, fee) => sum + Number(fee.amount || 0), 0)\r\n    },\r\n\r\n    // 删除法诉费用行\r\n    removeLitigationFee(index) {\r\n      this.litigationFees.splice(index, 1)\r\n      this.handleLitigationFeeAmountChange()\r\n    },\r\n\r\n    submitForm() {\r\n      // 验证是否有费用项目\r\n      if (this.litigationFees.length === 0) {\r\n        this.$message.warning('请至少添加一项费用！')\r\n        return\r\n      }\r\n\r\n      // 验证所有费用项目是否完整\r\n      const hasIncompleteItem = this.litigationFees.some(fee => !fee.type || !fee.amount)\r\n      if (hasIncompleteItem) {\r\n        this.$message.warning('请完善所有费用项目的类型和金额！')\r\n        return\r\n      }\r\n\r\n      // 分离日常费用和其他费用\r\n      const dailyExpenses = this.litigationFees.filter(fee => fee.type === 'dailyReimbursement')\r\n      const otherFees = this.litigationFees.filter(fee => fee.type !== 'dailyReimbursement')\r\n\r\n      // 如果有日常费用，提示用户\r\n      if (dailyExpenses.length > 0) {\r\n        this.$message.warning('检测到日常报销费用，请在\"日常费用审批\"模块中提交！')\r\n        return\r\n      }\r\n\r\n      // 构建提交数据\r\n      const litigationFee = { ...this.litigationFee }\r\n\r\n      // 确保 litigationCaseId 是数字类型\r\n      if (litigationFee.litigationCaseId) {\r\n        litigationFee.litigationCaseId = Number(litigationFee.litigationCaseId)\r\n      } else {\r\n        this.$message.error('缺少法诉案件ID，请重新选择案件')\r\n        return\r\n      }\r\n\r\n      // 将费用列表中的数据动态填入到 litigationFee 对象中\r\n      otherFees.forEach(({ type, amount }) => {\r\n        if (type) litigationFee[type] = amount || 0\r\n      })\r\n\r\n      console.log('提交的法诉费用数据:', litigationFee)\r\n\r\n      // 调用 API 提交数据\r\n      addLitigation_cost(litigationFee).then(res => {\r\n        if (res.code === 200) {\r\n          this.$message.success('提交成功')\r\n          this.dialogVisible = false\r\n          this.reset()\r\n          // 刷新父组件数据\r\n          this.$emit('refresh')\r\n        } else {\r\n          this.$message.error('提交失败：' + (res.msg || '未知错误'))\r\n        }\r\n      }).catch(error => {\r\n        console.error('提交失败:', error)\r\n        this.$message.error('提交失败，请稍后重试')\r\n      })\r\n    },\r\n\r\n    cancel() {\r\n      this.dialogVisible = false\r\n      this.reset()\r\n    },\r\n\r\n    open() {\r\n      this.reset()\r\n      this.dialogVisible = true\r\n    },\r\n\r\n    // 检查已提交的限制性费用类型\r\n    checkSubmittedLimitedTypes(litigationCaseId) {\r\n      if (!litigationCaseId) return\r\n\r\n      // 调用后端API检查已提交的判决金额和利息\r\n      checkSubmittedLimitedFees(litigationCaseId).then(res => {\r\n        if (res.code === 200) {\r\n          this.submittedLimitedTypes = res.data || []\r\n        }\r\n      }).catch(error => {\r\n        console.error('检查限制性费用类型失败:', error)\r\n        this.submittedLimitedTypes = []\r\n      })\r\n    },\r\n\r\n    // 打开日常费用申请弹窗\r\n    openDailyExpenseDialog(litigationCaseId = null) {\r\n      this.resetDailyExpenseForm()\r\n      this.dailyExpenseForm.litigationCaseId = String(litigationCaseId || this.litigationFee.litigationCaseId || '')\r\n      this.dailyExpenseForm.status = 1 // 法诉提交\r\n      this.dailyExpenseForm.applicantId = String(this.$store.state.user?.id || '')\r\n      this.dailyExpenseForm.applicantName = String(this.$store.state.user?.name || '')\r\n      this.dailyExpenseForm.applicationTime = new Date().toISOString().split('T')[0]\r\n      this.dailyExpenseDialogVisible = true\r\n    },\r\n\r\n    // 取消日常费用申请\r\n    cancelDailyExpense() {\r\n      this.dailyExpenseDialogVisible = false\r\n      this.resetDailyExpenseForm()\r\n    },\r\n\r\n    // 提交日常费用申请\r\n    submitDailyExpense() {\r\n      this.$refs.dailyExpenseForm.validate((valid) => {\r\n        if (valid) {\r\n          // 法诉提交：确保有 litigationCaseId 和 status = 1\r\n          const submitData = { ...this.dailyExpenseForm }\r\n          if (!submitData.litigationCaseId) {\r\n            this.$message.error('缺少法诉案件ID，请重新选择案件')\r\n            return\r\n          }\r\n          submitData.status = 1 // 确保是法诉提交\r\n          addDaily_expense_approval(submitData).then(res => {\r\n            if (res.code === 200) {\r\n              this.$message.success('日常费用申请提交成功')\r\n              this.dailyExpenseDialogVisible = false\r\n              this.resetDailyExpenseForm()\r\n            } else {\r\n              this.$message.error('提交失败：' + (res.msg || '未知错误'))\r\n            }\r\n          }).catch(error => {\r\n            console.error('提交失败:', error)\r\n            this.$message.error('提交失败，请稍后重试')\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 重置日常费用表单\r\n    resetDailyExpenseForm() {\r\n      this.dailyExpenseForm = {\r\n        litigationCaseId: '',\r\n        loanId: null,\r\n        status: 1, // 法诉提交\r\n        expenseType: '',\r\n        expenseAmount: '',\r\n        expenseDate: '',\r\n        expenseDescription: '',\r\n        applicantId: '',\r\n        applicantName: '',\r\n        applicationTime: '',\r\n        approvalStatus: '0'\r\n      }\r\n      if (this.$refs.dailyExpenseForm) {\r\n        this.$refs.dailyExpenseForm.resetFields()\r\n      }\r\n    },\r\n\r\n    reset() {\r\n      // 保留 litigationCaseId，只重置其他字段\r\n      const currentLitigationCaseId = this.litigationFee.litigationCaseId\r\n      this.litigationFee = {\r\n        litigationCaseId: currentLitigationCaseId,\r\n        贷款人: this.litigationFee.贷款人,\r\n        出单渠道: this.litigationFee.出单渠道,\r\n        放款银行: this.litigationFee.放款银行,\r\n        totalMoney: 0,\r\n      }\r\n      this.litigationFees = []\r\n      // 不重置 submittedLimitedTypes，因为这是从后端获取的状态\r\n    },\r\n\r\n    // 查看日常费用列表\r\n    viewDailyExpenseList() {\r\n      this.dailyExpenseListLoading = true\r\n      this.dailyExpenseListDialogVisible = true\r\n\r\n      const queryParams = {\r\n        litigationCaseId: this.litigationFee.litigationCaseId,\r\n        pageNum: 1,\r\n        pageSize: 100\r\n      }\r\n\r\n      listDaily_expense_approval(queryParams).then(res => {\r\n        if (res.code === 200) {\r\n          this.dailyExpenseList = res.rows || []\r\n        } else {\r\n          this.$message.error('获取日常费用列表失败：' + (res.msg || '未知错误'))\r\n          this.dailyExpenseList = []\r\n        }\r\n        this.dailyExpenseListLoading = false\r\n      }).catch(error => {\r\n        console.error('获取日常费用列表失败:', error)\r\n        this.$message.error('获取日常费用列表失败，请稍后重试')\r\n        this.dailyExpenseList = []\r\n        this.dailyExpenseListLoading = false\r\n      })\r\n    },\r\n\r\n    // 获取费用类型标签\r\n    getExpenseTypeLabel(type) {\r\n      const typeMap = {\r\n        'oil_fee': '油费',\r\n        'road_fee': '路费',\r\n        'meal_fee': '餐费',\r\n        'accommodation_fee': '住宿费',\r\n        'transport_fee': '交通费',\r\n        'parking_fee': '停车费',\r\n        'communication_fee': '通讯费',\r\n        'other': '其他'\r\n      }\r\n      return typeMap[type] || type\r\n    },\r\n\r\n    // 获取审批状态文本\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '待审批',\r\n        '1': '全部通过',\r\n        '2': '已拒绝',\r\n        '3': '主管审批中',\r\n        '4': '总监审批中',\r\n        '5': '财务主管审批中',\r\n        '6': '总经理审批中'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    // 获取审批状态标签类型\r\n    getStatusTagType(status) {\r\n      const typeMap = {\r\n        '0': 'warning',    // 待审批 - 橙色\r\n        '1': 'success',    // 全部通过 - 绿色\r\n        '2': 'danger',     // 已拒绝 - 红色\r\n        '3': 'primary',    // 主管审批中 - 蓝色\r\n        '4': 'primary',    // 总监审批中 - 蓝色\r\n        '5': 'primary',    // 财务主管审批中 - 蓝色\r\n        '6': 'primary'     // 总经理审批中 - 蓝色\r\n      }\r\n      return typeMap[status] || 'info'\r\n    }\r\n  },\r\n}\r\n</script>\r\n"]}]}