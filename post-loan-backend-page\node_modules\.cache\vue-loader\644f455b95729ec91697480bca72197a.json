{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation_cost_approval\\litigation_cost_approval\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation_cost_approval\\litigation_cost_approval\\index.vue", "mtime": 1754124854783}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdFBlbmRpbmdBcHByb3ZhbCwKICBnZXRMaXRpZ2F0aW9uQ29zdFN1Ym1pc3Npb25SZWNvcmRzLAogIHNpbmdsZUFwcHJvdmVMaXRpZ2F0aW9uQ29zdCwKICBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdE5ldwp9IGZyb20gIkAvYXBpL2xpdGlnYXRpb25fY29zdF9hcHByb3ZhbC9saXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWwiCmltcG9ydCBBcHByb3ZhbE1hbmFnZXIgZnJvbSAiQC91dGlscy9hcHByb3ZhbFN0YXR1cyIKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTGl0aWdhdGlvbkNvc3RBcHByb3ZhbCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDms5Xor4notLnnlKjlrqHmibnooajmoLzmlbDmja4KICAgICAgbGl0aWdhdGlvbkNvc3RBcHByb3ZhbExpc3Q6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBhcHByb3ZhbFN0YXR1czogbnVsbCwKICAgICAgfSwKICAgICAgLy8g6K+m5oOF5a+56K+d5qGG5pi+56S654q25oCBCiAgICAgIGRldGFpbHNEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g5b2T5YmN5qGI5Lu25L+h5oGvCiAgICAgIGN1cnJlbnRDYXNlSW5mbzogbnVsbCwKICAgICAgLy8g6LS555So6K6w5b2V5YiX6KGoCiAgICAgIGZlZVJlY29yZHM6IFtdLAogICAgICAvLyDotLnnlKjorrDlvZXliqDovb3nirbmgIEKICAgICAgcmVjb3Jkc0xvYWRpbmc6IGZhbHNlLAogICAgICAvLyDpgInkuK3nmoTotLnnlKjorrDlvZUKICAgICAgc2VsZWN0ZWRSZWNvcmRzOiBbXSwKICAgICAgLy8g5Y2V5Liq5a6h5om55a+56K+d5qGGCiAgICAgIHNpbmdsZUFwcHJvdmFsRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHNpbmdsZUFwcHJvdmFsRm9ybTogewogICAgICAgIGlkOiAnJywKICAgICAgICBhY3Rpb246ICcnLCAvLyAnYXBwcm92ZScg5oiWICdyZWplY3QnCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJwogICAgICB9LAogICAgICAvLyDmibnph4/lrqHmibnlr7nor53moYYKICAgICAgYmF0Y2hBcHByb3ZhbERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBiYXRjaEFwcHJvdmFsRm9ybTogewogICAgICAgIGFjdGlvbjogJycsIC8vICdhcHByb3ZlJyDmiJYgJ3JlamVjdCcKICAgICAgICByZWplY3RSZWFzb246ICcnCiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivouazleiviei0ueeUqOWuoeaJueWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICBsaXN0UGVuZGluZ0FwcHJvdmFsKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGl0aWdhdGlvbkNvc3RBcHByb3ZhbExpc3QgPSByZXNwb25zZS5yb3dzCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQogICAgICB0aGlzLmdldExpc3QoKQogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ2xpdGlnYXRpb25fY29zdF9hcHByb3ZhbC9saXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWwvZXhwb3J0JywgewogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMKICAgICAgfSwgYGxpdGlnYXRpb25fY29zdF9hcHByb3ZhbF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkKICAgIH0sCgogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLwogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgcmV0dXJuIEFwcHJvdmFsTWFuYWdlci5nZXRTdGF0dXNUZXh0KHBhcnNlSW50KHN0YXR1cykpCiAgICB9LAogICAgLyoqIOiOt+WPlueKtuaAgeagh+etvuexu+WeiyAqLwogICAgZ2V0U3RhdHVzVGFnVHlwZShzdGF0dXMpIHsKICAgICAgcmV0dXJuIEFwcHJvdmFsTWFuYWdlci5nZXRTdGF0dXNUYWdUeXBlKHBhcnNlSW50KHN0YXR1cykpCiAgICB9LAogICAgLyoqIOajgOafpeaYr+WQpuWPr+S7peWuoeaJuSAqLwogICAgY2FuQXBwcm92ZShzdGF0dXMpIHsKICAgICAgcmV0dXJuIEFwcHJvdmFsTWFuYWdlci5jYW5BcHByb3ZlKHBhcnNlSW50KHN0YXR1cykpCiAgICB9LAogICAgLyoqIOajgOafpeaYr+WQpuS4uuacgOe7iOeKtuaAgSAqLwogICAgaXNGaW5hbFN0YXR1cyhzdGF0dXMpIHsKICAgICAgcmV0dXJuIEFwcHJvdmFsTWFuYWdlci5pc0ZpbmFsU3RhdHVzKHBhcnNlSW50KHN0YXR1cykpCiAgICB9LAoKICAgIC8qKiDmn6XnnIvotLnnlKjor6bmg4XlkozlrqHmibkgKi8KICAgIGhhbmRsZVZpZXdEZXRhaWxzKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRDYXNlSW5mbyA9IHJvdwogICAgICB0aGlzLmRldGFpbHNEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgICB0aGlzLmxvYWRGZWVSZWNvcmRzKHJvdy5saXRpZ2F0aW9uQ2FzZUlkKQogICAgfSwKCiAgICAvKiog5Yqg6L296LS555So6K6w5b2VICovCiAgICBsb2FkRmVlUmVjb3JkcyhsaXRpZ2F0aW9uQ2FzZUlkKSB7CiAgICAgIHRoaXMucmVjb3Jkc0xvYWRpbmcgPSB0cnVlCiAgICAgIGdldExpdGlnYXRpb25Db3N0U3VibWlzc2lvblJlY29yZHMobGl0aWdhdGlvbkNhc2VJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5mZWVSZWNvcmRzID0gcmVzcG9uc2UuZGF0YSB8fCBbXQogICAgICAgIHRoaXMucmVjb3Jkc0xvYWRpbmcgPSBmYWxzZQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5yZWNvcmRzTG9hZGluZyA9IGZhbHNlCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+WKoOi9vei0ueeUqOiusOW9leWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDotLnnlKjorrDlvZXpgInmi6nlj5jljJYgKi8KICAgIGhhbmRsZVJlY29yZFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5zZWxlY3RlZFJlY29yZHMgPSBzZWxlY3Rpb24KICAgIH0sCgogICAgLyoqIOWNleS4quWuoeaJuSAqLwogICAgaGFuZGxlU2luZ2xlQXBwcm92ZShyZWNvcmQsIGFjdGlvbikgewogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5pZCA9IHJlY29yZC5pZAogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPSBhY3Rpb24KICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uID0gJycKICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbERpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICB9LAoKICAgIC8qKiDnoa7orqTljZXkuKrlrqHmibkgKi8KICAgIGNvbmZpcm1TaW5nbGVBcHByb3ZhbCgpIHsKICAgICAgaWYgKHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ3JlamVjdCcpIHsKICAgICAgICB0aGlzLiRyZWZzWyJzaW5nbGVBcHByb3ZhbEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4KICAgICAgICAgIHRoaXMuZXhlY3V0ZVNpbmdsZUFwcHJvdmFsKCkKICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZXhlY3V0ZVNpbmdsZUFwcHJvdmFsKCkKICAgICAgfQogICAgfSwKCiAgICAvKiog5omn6KGM5Y2V5Liq5a6h5om5ICovCiAgICBleGVjdXRlU2luZ2xlQXBwcm92YWwoKSB7CiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgaWQ6IHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmlkLAogICAgICAgIGFjdGlvbjogdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uYWN0aW9uLAogICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uCiAgICAgIH0KCiAgICAgIHNpbmdsZUFwcHJvdmVMaXRpZ2F0aW9uQ29zdChkYXRhKS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKGAke3RoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gJ+mAmui/hycgOiAn5ouS57udJ33lrqHmibnmiJDlip9gKQogICAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxEaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgICB0aGlzLmxvYWRGZWVSZWNvcmRzKHRoaXMuY3VycmVudENhc2VJbmZvLmxpdGlnYXRpb25DYXNlSWQpCiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCflrqHmibnlpLHotKUnKQogICAgICB9KQogICAgfSwKCiAgICAvKiog5om56YeP5a6h5om5ICovCiAgICBoYW5kbGVCYXRjaEFwcHJvdmUoYWN0aW9uKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUmVjb3Jkcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6K+36YCJ5oup6KaB5a6h5om555qE6K6w5b2VJykKICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5hY3Rpb24gPSBhY3Rpb24KICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24gPSAnJwogICAgICB0aGlzLmJhdGNoQXBwcm92YWxEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvKiog56Gu6K6k5om56YeP5a6h5om5ICovCiAgICBjb25maXJtQmF0Y2hBcHByb3ZhbCgpIHsKICAgICAgaWYgKHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAncmVqZWN0JykgewogICAgICAgIHRoaXMuJHJlZnNbImJhdGNoQXBwcm92YWxGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuCiAgICAgICAgICB0aGlzLmV4ZWN1dGVCYXRjaEFwcHJvdmFsKCkKICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZXhlY3V0ZUJhdGNoQXBwcm92YWwoKQogICAgICB9CiAgICB9LAoKICAgIC8qKiDmiafooYzmibnph4/lrqHmibkgKi8KICAgIGV4ZWN1dGVCYXRjaEFwcHJvdmFsKCkgewogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGlkczogdGhpcy5zZWxlY3RlZFJlY29yZHMubWFwKHJlY29yZCA9PiByZWNvcmQuaWQpLAogICAgICAgIGFjdGlvbjogdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5hY3Rpb24sCiAgICAgICAgcmVqZWN0UmVhc29uOiB0aGlzLmJhdGNoQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbgogICAgICB9CgogICAgICBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdE5ldyhkYXRhKS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKGDmibnph48ke3RoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAnYXBwcm92ZScgPyAn6YCa6L+HJyA6ICfmi5Lnu50nfeWuoeaJueaIkOWKn2ApCiAgICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsRGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgdGhpcy5sb2FkRmVlUmVjb3Jkcyh0aGlzLmN1cnJlbnRDYXNlSW5mby5saXRpZ2F0aW9uQ2FzZUlkKQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5om56YeP5a6h5om55aSx6LSlJykKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/litigation_cost_approval/litigation_cost_approval", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"litigationCostApprovalList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"案件编号\" align=\"center\" prop=\"caseNumber\" />\n      <el-table-column label=\"案件名称\" align=\"center\" prop=\"caseName\" />\n      <el-table-column label=\"被告姓名\" align=\"center\" prop=\"defendantName\" />\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.lawyerFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.litigationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.preservationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.totalMoney || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n            {{ getStatusText(scope.row.approvalStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleViewDetails(scope.row)\"\n            v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\"\n          >审批</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 费用详情审批对话框 -->\n    <el-dialog title=\"法诉费用审批\" :visible.sync=\"detailsDialogVisible\" width=\"1200px\" append-to-body>\n      <!-- 案件基本信息 -->\n      <el-descriptions :column=\"3\" border style=\"margin-bottom: 20px\" v-if=\"currentCaseInfo\">\n        <el-descriptions-item label=\"案件编号\">{{ currentCaseInfo.caseNumber }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件名称\">{{ currentCaseInfo.caseName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告姓名\">{{ currentCaseInfo.defendantName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告身份证\">{{ currentCaseInfo.defendantIdCard }}</el-descriptions-item>\n        <el-descriptions-item label=\"法院管辖\">{{ currentCaseInfo.courtLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件书记员\">{{ currentCaseInfo.curator }}</el-descriptions-item>\n      </el-descriptions>\n\n      <!-- 费用记录列表 -->\n      <div style=\"margin-bottom: 20px;\">\n        <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;\">\n          <h4>费用记录</h4>\n          <div>\n            <el-button\n              type=\"success\"\n              size=\"small\"\n              @click=\"handleBatchApprove('approve')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量通过</el-button>\n            <el-button\n              type=\"danger\"\n              size=\"small\"\n              @click=\"handleBatchApprove('reject')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量拒绝</el-button>\n          </div>\n        </div>\n\n        <el-table\n          :data=\"feeRecords\"\n          @selection-change=\"handleRecordSelectionChange\"\n          v-loading=\"recordsLoading\"\n          border>\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}') }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\n          <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.lawyerFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.litigationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.preservationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.surveillanceFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.announcementFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.appraisalFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.executionFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.totalMoney || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n                {{ getStatusText(scope.row.approvalStatus) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}') : '-' }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\n          <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" show-overflow-tooltip />\n          <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'approve')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >通过</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'reject')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >拒绝</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 单个审批确认对话框 -->\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"singleApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"singleApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 批量审批确认对话框 -->\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"选中记录\">\n          <span>{{ selectedRecords.length }} 条记录</span>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"batchApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"batchApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listPendingApproval,\n  getLitigationCostSubmissionRecords,\n  singleApproveLitigationCost,\n  batchApproveLitigationCostNew\n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\nimport ApprovalManager from \"@/utils/approvalStatus\"\n\nexport default {\n  name: \"LitigationCostApproval\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 法诉费用审批表格数据\n      litigationCostApprovalList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        approvalStatus: null,\n      },\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前案件信息\n      currentCaseInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询法诉费用审批列表 */\n    getList() {\n      this.loading = true\n      listPendingApproval(this.queryParams).then(response => {\n        this.litigationCostApprovalList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\n        ...this.queryParams\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\n    },\n\n    /** 获取状态文本 */\n    getStatusText(status) {\n      return ApprovalManager.getStatusText(parseInt(status))\n    },\n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      return ApprovalManager.getStatusTagType(parseInt(status))\n    },\n    /** 检查是否可以审批 */\n    canApprove(status) {\n      return ApprovalManager.canApprove(parseInt(status))\n    },\n    /** 检查是否为最终状态 */\n    isFinalStatus(status) {\n      return ApprovalManager.isFinalStatus(parseInt(status))\n    },\n\n    /** 查看费用详情和审批 */\n    handleViewDetails(row) {\n      this.currentCaseInfo = row\n      this.detailsDialogVisible = true\n      this.loadFeeRecords(row.litigationCaseId)\n    },\n\n    /** 加载费用记录 */\n    loadFeeRecords(litigationCaseId) {\n      this.recordsLoading = true\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\n        this.feeRecords = response.data || []\n        this.recordsLoading = false\n      }).catch(() => {\n        this.recordsLoading = false\n        this.$modal.msgError('加载费用记录失败')\n      })\n    },\n\n    /** 费用记录选择变化 */\n    handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection\n    },\n\n    /** 单个审批 */\n    handleSingleApprove(record, action) {\n      this.singleApprovalForm.id = record.id\n      this.singleApprovalForm.action = action\n      this.singleApprovalForm.rejectReason = ''\n      this.singleApprovalDialogVisible = true\n    },\n\n    /** 确认单个审批 */\n    confirmSingleApproval() {\n      if (this.singleApprovalForm.action === 'reject') {\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\n          if (!valid) return\n          this.executeSingleApproval()\n        })\n      } else {\n        this.executeSingleApproval()\n      }\n    },\n\n    /** 执行单个审批 */\n    executeSingleApproval() {\n      const data = {\n        id: this.singleApprovalForm.id,\n        action: this.singleApprovalForm.action,\n        rejectReason: this.singleApprovalForm.rejectReason\n      }\n\n      singleApproveLitigationCost(data).then(() => {\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\n        this.singleApprovalDialogVisible = false\n        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)\n        this.getList()\n      }).catch(() => {\n        this.$modal.msgError('审批失败')\n      })\n    },\n\n    /** 批量审批 */\n    handleBatchApprove(action) {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError('请选择要审批的记录')\n        return\n      }\n\n      this.batchApprovalForm.action = action\n      this.batchApprovalForm.rejectReason = ''\n      this.batchApprovalDialogVisible = true\n    },\n\n    /** 确认批量审批 */\n    confirmBatchApproval() {\n      if (this.batchApprovalForm.action === 'reject') {\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\n          if (!valid) return\n          this.executeBatchApproval()\n        })\n      } else {\n        this.executeBatchApproval()\n      }\n    },\n\n    /** 执行批量审批 */\n    executeBatchApproval() {\n      const data = {\n        ids: this.selectedRecords.map(record => record.id),\n        action: this.batchApprovalForm.action,\n        rejectReason: this.batchApprovalForm.rejectReason\n      }\n\n      batchApproveLitigationCostNew(data).then(() => {\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\n        this.batchApprovalDialogVisible = false\n        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)\n        this.getList()\n      }).catch(() => {\n        this.$modal.msgError('批量审批失败')\n      })\n    }\n  }\n}\n</script>\n"]}]}